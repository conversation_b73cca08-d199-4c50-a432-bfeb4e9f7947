# 第1章：Swoole 简介

## 学习目标
- 了解 Swoole 的发展历史和核心特性
- 理解 Swoole 与传统 PHP 的区别
- 掌握 Swoole 的应用场景和优势
- 了解 Swoole 的生态系统

## 1.1 什么是 Swoole

Swoole 是一个使用 C++ 语言编写的基于异步事件驱动和协程的并发网络通信引擎，为 PHP 提供了协程、高性能网络编程支持。它可以让 PHP 开发者可以编写高性能的异步并发 TCP、UDP、Unix Socket、HTTP、WebSocket 服务。

### 1.1.1 Swoole 的核心特性

1. **协程支持**
   - 提供了完整的协程实现
   - 支持协程调度器
   - 协程间通信机制

2. **异步非阻塞 I/O**
   - 基于 epoll/kqueue 事件循环
   - 异步文件操作
   - 异步网络操作

3. **多进程/多线程**
   - Master-Worker 进程模型
   - 进程池管理
   - 进程间通信

4. **内置服务器**
   - HTTP/HTTPS 服务器
   - WebSocket 服务器
   - TCP/UDP 服务器

## 1.2 Swoole 的发展历史

### 1.2.1 发展时间线

- **2012年**：Swoole 项目启动，由韩天峰（Rango）创建
- **2014年**：发布 1.0 版本，提供基础的异步网络编程能力
- **2016年**：发布 2.0 版本，引入协程概念
- **2018年**：发布 4.0 版本，协程成为核心特性
- **2020年**：发布 4.5 版本，协程生态更加完善
- **2021年**：发布 4.6 版本，性能进一步优化
- **2023年**：发布 5.0 版本，全面拥抱协程

### 1.2.2 版本特性演进

| 版本 | 主要特性 | 发布时间 |
|------|----------|----------|
| 1.x | 异步回调模式 | 2014 |
| 2.x | 引入协程概念 | 2016 |
| 4.x | 协程成为主流 | 2018 |
| 5.x | 全协程化 | 2023 |

## 1.3 Swoole vs 传统 PHP

### 1.3.1 传统 PHP 的局限性

```php
// 传统 PHP 同步阻塞模式
$data1 = file_get_contents('http://api1.example.com');
$data2 = file_get_contents('http://api2.example.com');
$data3 = file_get_contents('http://api3.example.com');
// 总耗时 = 请求1耗时 + 请求2耗时 + 请求3耗时
```

**问题：**
- 同步阻塞执行
- 无法充分利用系统资源
- 并发能力有限
- 内存占用高

### 1.3.2 Swoole 的优势

```php
// Swoole 协程并发模式
use Swoole\Coroutine;
use function Swoole\Coroutine\run;

run(function () {
    $results = [];
    
    // 并发执行三个请求
    Coroutine::create(function () use (&$results) {
        $results[1] = file_get_contents('http://api1.example.com');
    });
    
    Coroutine::create(function () use (&$results) {
        $results[2] = file_get_contents('http://api2.example.com');
    });
    
    Coroutine::create(function () use (&$results) {
        $results[3] = file_get_contents('http://api3.example.com');
    });
    
    // 总耗时 ≈ max(请求1耗时, 请求2耗时, 请求3耗时)
});
```

**优势：**
- 异步非阻塞执行
- 高并发处理能力
- 低内存占用
- 更好的性能表现

## 1.4 Swoole 的应用场景

### 1.4.1 Web 服务器
- 高性能 HTTP 服务器
- API 网关
- 微服务架构

### 1.4.2 实时通信
- WebSocket 服务器
- 即时聊天系统
- 实时推送服务

### 1.4.3 网络服务
- TCP/UDP 服务器
- 代理服务器
- 负载均衡器

### 1.4.4 后台服务
- 任务队列处理
- 定时任务系统
- 数据同步服务

## 1.5 性能对比

### 1.5.1 并发性能测试

| 服务器类型 | QPS | 内存占用 | CPU 使用率 |
|------------|-----|----------|------------|
| Apache + PHP-FPM | 1,000 | 512MB | 80% |
| Nginx + PHP-FPM | 2,000 | 256MB | 60% |
| Swoole HTTP Server | 10,000+ | 128MB | 40% |

### 1.5.2 内存使用对比

```php
// 传统 PHP-FPM 模式
// 每个请求启动一个进程，处理完毕后销毁
// 内存使用：进程数 × 每进程内存

// Swoole 常驻内存模式
// 进程常驻内存，复用连接和资源
// 内存使用：固定进程数 × 每进程内存
```

## 1.6 Swoole 生态系统

### 1.6.1 官方组件
- **Swoole\Coroutine**：协程库
- **Swoole\Http\Server**：HTTP 服务器
- **Swoole\WebSocket\Server**：WebSocket 服务器
- **Swoole\Server**：TCP/UDP 服务器

### 1.6.2 第三方框架
- **Hyperf**：基于 Swoole 的高性能框架
- **EasySwoole**：简单易用的 Swoole 框架
- **Swoft**：微服务框架
- **MixPHP**：单机和分布式框架

### 1.6.3 工具和库
- **Swoole Tracker**：性能监控工具
- **Swoole Compiler**：代码加密工具
- **协程客户端库**：MySQL、Redis、HTTP 等

## 1.7 学习路径建议

### 1.7.1 基础阶段
1. 理解协程概念
2. 掌握基本 API 使用
3. 学习事件驱动编程

### 1.7.2 进阶阶段
1. 深入理解进程模型
2. 掌握性能优化技巧
3. 学习架构设计模式

### 1.7.3 高级阶段
1. 源码分析
2. 扩展开发
3. 生产环境实践

## 本章练习

### 练习 1：环境检查
编写一个 PHP 脚本，检查当前环境是否支持 Swoole：

```php
<?php
// check_swoole.php

echo "PHP 版本检查:\n";
echo "当前 PHP 版本: " . PHP_VERSION . "\n";

if (version_compare(PHP_VERSION, '7.2.0', '<')) {
    echo "❌ PHP 版本过低，需要 7.2.0 或更高版本\n";
} else {
    echo "✅ PHP 版本符合要求\n";
}

echo "\nSwoole 扩展检查:\n";
if (extension_loaded('swoole')) {
    echo "✅ Swoole 扩展已安装\n";
    echo "Swoole 版本: " . SWOOLE_VERSION . "\n";
    
    // 检查关键特性
    $features = [
        'HTTP2' => defined('SWOOLE_HTTP2'),
        'SSL' => defined('SWOOLE_SSL'),
        'Coroutine' => class_exists('Swoole\Coroutine'),
    ];
    
    foreach ($features as $feature => $available) {
        echo ($available ? "✅" : "❌") . " {$feature} 支持\n";
    }
} else {
    echo "❌ Swoole 扩展未安装\n";
}
?>
```

### 练习 2：性能对比测试
创建两个脚本，对比同步和异步请求的性能差异：

```php
<?php
// sync_test.php - 同步请求测试

$start = microtime(true);

// 模拟 3 个耗时 1 秒的操作
sleep(1);
sleep(1);
sleep(1);

$end = microtime(true);
echo "同步执行耗时: " . ($end - $start) . " 秒\n";
?>
```

```php
<?php
// async_test.php - 异步请求测试

use function Swoole\Coroutine\run;
use Swoole\Coroutine;

run(function () {
    $start = microtime(true);
    
    // 并发执行 3 个耗时 1 秒的操作
    $tasks = [];
    for ($i = 0; $i < 3; $i++) {
        $tasks[] = Coroutine::create(function () {
            Coroutine::sleep(1); // 协程睡眠，不阻塞其他协程
        });
    }
    
    $end = microtime(true);
    echo "异步执行耗时: " . ($end - $start) . " 秒\n";
});
?>
```

### 练习 3：思考题
1. 为什么 Swoole 能够提供比传统 PHP 更高的性能？
2. 协程和多线程有什么区别？
3. 在什么场景下应该选择使用 Swoole？
4. Swoole 的常驻内存模式有什么优缺点？

## 本章小结

本章介绍了 Swoole 的基本概念、发展历史、核心特性和应用场景。通过与传统 PHP 的对比，我们了解了 Swoole 的优势所在。在后续章节中，我们将深入学习 Swoole 的具体使用方法和高级特性。

**关键要点：**
- Swoole 是基于协程的高性能网络通信引擎
- 提供异步非阻塞 I/O 和协程支持
- 适用于高并发、实时通信等场景
- 拥有丰富的生态系统和工具支持

**下一章预告：**
下一章我们将学习如何安装和配置 Swoole 环境，为后续的学习做好准备。
