# 第7章：路由系统

## 学习目标
- 理解路由系统的设计原理
- 掌握静态路由和动态路由的实现
- 学会路由参数提取和验证
- 了解路由中间件和路由组的使用

## 7.1 路由基础概念

### 7.1.1 什么是路由

路由是 Web 应用中将 URL 映射到具体处理函数的机制。它决定了当用户访问特定 URL 时，应该执行哪个代码逻辑。

```php
<?php
// basic_router.php

use Swoole\Http\Server;

class SimpleRouter {
    private $routes = [];
    
    public function get($path, $handler) {
        $this->addRoute('GET', $path, $handler);
    }
    
    public function post($path, $handler) {
        $this->addRoute('POST', $path, $handler);
    }
    
    public function put($path, $handler) {
        $this->addRoute('PUT', $path, $handler);
    }
    
    public function delete($path, $handler) {
        $this->addRoute('DELETE', $path, $handler);
    }
    
    private function addRoute($method, $path, $handler) {
        $this->routes[$method][$path] = $handler;
    }
    
    public function dispatch($method, $uri) {
        // 移除查询字符串
        $path = parse_url($uri, PHP_URL_PATH);
        
        if (isset($this->routes[$method][$path])) {
            return $this->routes[$method][$path];
        }
        
        return null;
    }
}

$router = new SimpleRouter();

// 定义路由
$router->get('/', function($request, $response) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '欢迎访问首页']));
});

$router->get('/about', function($request, $response) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '关于我们页面']));
});

$router->post('/users', function($request, $response) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '创建用户', 'data' => $request->post]));
});

$router->get('/api/status', function($request, $response) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'status' => 'ok',
        'timestamp' => time(),
        'server' => 'Swoole'
    ]));
});

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use ($router) {
    $method = $request->server['request_method'];
    $uri = $request->server['request_uri'];
    
    $handler = $router->dispatch($method, $uri);
    
    if ($handler) {
        $handler($request, $response);
    } else {
        $response->status(404);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Route not found']));
    }
});

echo "简单路由服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

### 7.1.2 路由匹配算法

```php
<?php
// route_matching.php

class RoutePattern {
    private $pattern;
    private $paramNames = [];
    private $regex;
    
    public function __construct($pattern) {
        $this->pattern = $pattern;
        $this->compilePattern();
    }
    
    private function compilePattern() {
        // 转换路由模式为正则表达式
        $pattern = $this->pattern;
        
        // 处理参数占位符 {id}, {name} 等
        $pattern = preg_replace_callback('/\{([^}]+)\}/', function($matches) {
            $this->paramNames[] = $matches[1];
            return '([^/]+)';
        }, $pattern);
        
        // 处理可选参数 {id?}
        $pattern = preg_replace_callback('/\{([^}]+)\?\}/', function($matches) {
            $this->paramNames[] = $matches[1];
            return '([^/]*)';
        }, $pattern);
        
        // 转义特殊字符
        $pattern = str_replace('/', '\/', $pattern);
        
        $this->regex = '/^' . $pattern . '$/';
    }
    
    public function match($path) {
        if (preg_match($this->regex, $path, $matches)) {
            array_shift($matches); // 移除完整匹配
            
            $params = [];
            foreach ($this->paramNames as $index => $name) {
                $params[$name] = $matches[$index] ?? null;
            }
            
            return $params;
        }
        
        return false;
    }
    
    public function getPattern() {
        return $this->pattern;
    }
}

// 测试路由匹配
$patterns = [
    '/users/{id}',
    '/users/{id}/posts/{postId}',
    '/api/v{version}/users',
    '/files/{path?}',
    '/products/{category}/{id}',
];

$testPaths = [
    '/users/123',
    '/users/456/posts/789',
    '/api/v1/users',
    '/api/v2/users',
    '/files/',
    '/files/documents/readme.txt',
    '/products/electronics/laptop-123',
];

echo "=== 路由匹配测试 ===\n\n";

foreach ($patterns as $pattern) {
    $route = new RoutePattern($pattern);
    echo "模式: {$pattern}\n";
    
    foreach ($testPaths as $path) {
        $result = $route->match($path);
        if ($result !== false) {
            echo "  ✓ {$path} -> " . json_encode($result) . "\n";
        }
    }
    echo "\n";
}
?>
```

## 7.2 高级路由系统

### 7.2.1 完整的路由器实现

```php
<?php
// advanced_router.php

use Swoole\Http\Server;

class Route {
    public $method;
    public $pattern;
    public $handler;
    public $middleware = [];
    public $name;
    public $constraints = [];
    
    public function __construct($method, $pattern, $handler) {
        $this->method = $method;
        $this->pattern = $pattern;
        $this->handler = $handler;
    }
    
    public function middleware($middleware) {
        if (is_array($middleware)) {
            $this->middleware = array_merge($this->middleware, $middleware);
        } else {
            $this->middleware[] = $middleware;
        }
        return $this;
    }
    
    public function name($name) {
        $this->name = $name;
        return $this;
    }
    
    public function where($param, $pattern) {
        $this->constraints[$param] = $pattern;
        return $this;
    }
}

class Router {
    private $routes = [];
    private $namedRoutes = [];
    private $groupStack = [];
    private $middleware = [];
    
    public function get($pattern, $handler) {
        return $this->addRoute('GET', $pattern, $handler);
    }
    
    public function post($pattern, $handler) {
        return $this->addRoute('POST', $pattern, $handler);
    }
    
    public function put($pattern, $handler) {
        return $this->addRoute('PUT', $pattern, $handler);
    }
    
    public function delete($pattern, $handler) {
        return $this->addRoute('DELETE', $pattern, $handler);
    }
    
    public function any($pattern, $handler) {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        $routes = [];
        foreach ($methods as $method) {
            $routes[] = $this->addRoute($method, $pattern, $handler);
        }
        return $routes;
    }
    
    private function addRoute($method, $pattern, $handler) {
        // 应用路由组前缀
        $pattern = $this->applyGroupPrefix($pattern);
        
        $route = new Route($method, $pattern, $handler);
        
        // 应用路由组中间件
        $route->middleware($this->getGroupMiddleware());
        
        $this->routes[] = $route;
        
        return $route;
    }
    
    public function group($attributes, $callback) {
        $this->groupStack[] = $attributes;
        $callback($this);
        array_pop($this->groupStack);
    }
    
    private function applyGroupPrefix($pattern) {
        $prefix = '';
        foreach ($this->groupStack as $group) {
            if (isset($group['prefix'])) {
                $prefix .= '/' . trim($group['prefix'], '/');
            }
        }
        
        return $prefix . $pattern;
    }
    
    private function getGroupMiddleware() {
        $middleware = [];
        foreach ($this->groupStack as $group) {
            if (isset($group['middleware'])) {
                $middleware = array_merge($middleware, (array)$group['middleware']);
            }
        }
        return $middleware;
    }
    
    public function dispatch($method, $uri) {
        $path = parse_url($uri, PHP_URL_PATH);
        
        foreach ($this->routes as $route) {
            if ($route->method !== $method) {
                continue;
            }
            
            $params = $this->matchRoute($route, $path);
            if ($params !== false) {
                return [
                    'route' => $route,
                    'params' => $params
                ];
            }
        }
        
        return null;
    }
    
    private function matchRoute($route, $path) {
        $pattern = $route->pattern;
        $paramNames = [];
        
        // 转换路由模式为正则表达式
        $regex = preg_replace_callback('/\{([^}]+)\}/', function($matches) use (&$paramNames, $route) {
            $paramName = $matches[1];
            $paramNames[] = $paramName;
            
            // 检查参数约束
            if (isset($route->constraints[$paramName])) {
                return '(' . $route->constraints[$paramName] . ')';
            }
            
            return '([^/]+)';
        }, $pattern);
        
        $regex = '/^' . str_replace('/', '\/', $regex) . '$/';
        
        if (preg_match($regex, $path, $matches)) {
            array_shift($matches); // 移除完整匹配
            
            $params = [];
            foreach ($paramNames as $index => $name) {
                $params[$name] = $matches[$index] ?? null;
            }
            
            return $params;
        }
        
        return false;
    }
    
    public function url($name, $params = []) {
        if (!isset($this->namedRoutes[$name])) {
            throw new Exception("Named route '{$name}' not found");
        }
        
        $route = $this->namedRoutes[$name];
        $url = $route->pattern;
        
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }
        
        return $url;
    }
}

// 中间件示例
function authMiddleware($request, $response, $next) {
    $token = $request->header['authorization'] ?? '';
    
    if (empty($token)) {
        $response->status(401);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Unauthorized']));
        return;
    }
    
    // 验证 token（这里简化处理）
    if ($token !== 'Bearer valid-token') {
        $response->status(401);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Invalid token']));
        return;
    }
    
    $next($request, $response);
}

function logMiddleware($request, $response, $next) {
    $start = microtime(true);
    
    echo "[" . date('Y-m-d H:i:s') . "] {$request->server['request_method']} {$request->server['request_uri']}\n";
    
    $next($request, $response);
    
    $duration = round((microtime(true) - $start) * 1000, 2);
    echo "  -> 处理耗时: {$duration}ms\n";
}

$router = new Router();

// 基础路由
$router->get('/', function($request, $response) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '首页']));
});

// 带参数的路由
$router->get('/users/{id}', function($request, $response, $params) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'message' => '用户详情',
        'user_id' => $params['id']
    ]));
})->where('id', '\d+')->name('user.show');

// 路由组
$router->group(['prefix' => 'api/v1', 'middleware' => 'logMiddleware'], function($router) {
    $router->get('/status', function($request, $response) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['status' => 'ok', 'version' => '1.0']));
    });
    
    // 需要认证的路由组
    $router->group(['middleware' => 'authMiddleware'], function($router) {
        $router->get('/profile', function($request, $response) {
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['message' => '用户资料']));
        });
        
        $router->post('/logout', function($request, $response) {
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['message' => '退出成功']));
        });
    });
});

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use ($router) {
    $method = $request->server['request_method'];
    $uri = $request->server['request_uri'];
    
    $result = $router->dispatch($method, $uri);
    
    if ($result) {
        $route = $result['route'];
        $params = $result['params'];
        
        // 执行中间件
        $middlewareStack = $route->middleware;
        $index = 0;
        
        $next = function($request, $response) use (&$next, &$middlewareStack, &$index, $route, $params) {
            if ($index < count($middlewareStack)) {
                $middleware = $middlewareStack[$index++];
                
                // 根据中间件名称获取实际函数
                if ($middleware === 'authMiddleware') {
                    authMiddleware($request, $response, $next);
                } elseif ($middleware === 'logMiddleware') {
                    logMiddleware($request, $response, $next);
                }
            } else {
                // 执行路由处理器
                $route->handler($request, $response, $params);
            }
        };
        
        $next($request, $response);
    } else {
        $response->status(404);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Route not found']));
    }
});

echo "高级路由服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "测试路由:\n";
echo "  GET /\n";
echo "  GET /users/123\n";
echo "  GET /api/v1/status\n";
echo "  GET /api/v1/profile (需要 Authorization: Bearer valid-token)\n";
$server->start();
?>
```

## 7.3 路由缓存和性能优化

### 7.3.1 路由缓存实现

```php
<?php
// cached_router.php

use Swoole\Http\Server;

class CachedRouter {
    private $routes = [];
    private $cache = [];
    private $cacheEnabled = true;
    private $cacheHits = 0;
    private $cacheMisses = 0;

    public function addRoute($method, $pattern, $handler) {
        $this->routes[] = [
            'method' => $method,
            'pattern' => $pattern,
            'handler' => $handler,
            'compiled' => $this->compilePattern($pattern)
        ];
    }

    private function compilePattern($pattern) {
        $paramNames = [];

        $regex = preg_replace_callback('/\{([^}]+)\}/', function($matches) use (&$paramNames) {
            $paramNames[] = $matches[1];
            return '([^/]+)';
        }, $pattern);

        $regex = '/^' . str_replace('/', '\/', $regex) . '$/';

        return [
            'regex' => $regex,
            'params' => $paramNames
        ];
    }

    public function dispatch($method, $uri) {
        $path = parse_url($uri, PHP_URL_PATH);
        $cacheKey = $method . ':' . $path;

        // 检查缓存
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            $this->cacheHits++;
            return $this->cache[$cacheKey];
        }

        $this->cacheMisses++;

        // 查找匹配的路由
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            if (preg_match($route['compiled']['regex'], $path, $matches)) {
                array_shift($matches); // 移除完整匹配

                $params = [];
                foreach ($route['compiled']['params'] as $index => $name) {
                    $params[$name] = $matches[$index] ?? null;
                }

                $result = [
                    'handler' => $route['handler'],
                    'params' => $params
                ];

                // 缓存结果
                if ($this->cacheEnabled) {
                    $this->cache[$cacheKey] = $result;
                }

                return $result;
            }
        }

        return null;
    }

    public function getStats() {
        return [
            'cache_hits' => $this->cacheHits,
            'cache_misses' => $this->cacheMisses,
            'hit_ratio' => $this->cacheHits + $this->cacheMisses > 0
                ? round($this->cacheHits / ($this->cacheHits + $this->cacheMisses) * 100, 2)
                : 0,
            'cached_routes' => count($this->cache)
        ];
    }

    public function clearCache() {
        $this->cache = [];
        $this->cacheHits = 0;
        $this->cacheMisses = 0;
    }
}

$router = new CachedRouter();

// 添加大量路由进行性能测试
$router->addRoute('GET', '/', function($request, $response) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '首页']));
});

$router->addRoute('GET', '/stats', function($request, $response) use ($router) {
    $stats = $router->getStats();
    $response->header("Content-Type", "application/json");
    $response->end(json_encode($stats, JSON_PRETTY_PRINT));
});

// 批量添加测试路由
for ($i = 1; $i <= 100; $i++) {
    $router->addRoute('GET', "/test/{$i}/{id}", function($request, $response, $params) use ($i) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'route' => $i,
            'params' => $params,
            'message' => "测试路由 {$i}"
        ]));
    });
}

$router->addRoute('GET', '/users/{id}', function($request, $response, $params) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'user_id' => $params['id'],
        'message' => '用户详情'
    ]));
});

$router->addRoute('GET', '/products/{category}/{id}', function($request, $response, $params) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'category' => $params['category'],
        'product_id' => $params['id'],
        'message' => '产品详情'
    ]));
});

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use ($router) {
    $method = $request->server['request_method'];
    $uri = $request->server['request_uri'];

    $start = microtime(true);
    $result = $router->dispatch($method, $uri);
    $duration = (microtime(true) - $start) * 1000;

    if ($result) {
        // 添加性能头部
        $response->header("X-Route-Time", round($duration, 3) . 'ms');

        $result['handler']($request, $response, $result['params']);
    } else {
        $response->status(404);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Route not found']));
    }
});

echo "缓存路由服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "性能测试路由: /test/1/123, /test/50/456, /users/789\n";
echo "查看缓存统计: /stats\n";
$server->start();
?>
```

### 7.3.2 路由预编译

```php
<?php
// precompiled_router.php

class RouteCompiler {
    public static function compile($routes) {
        $compiled = [
            'static' => [],
            'dynamic' => []
        ];

        foreach ($routes as $route) {
            if (strpos($route['pattern'], '{') === false) {
                // 静态路由
                $compiled['static'][$route['method']][$route['pattern']] = $route;
            } else {
                // 动态路由
                $compiled['dynamic'][$route['method']][] = [
                    'pattern' => $route['pattern'],
                    'regex' => self::compilePattern($route['pattern']),
                    'handler' => $route['handler']
                ];
            }
        }

        return $compiled;
    }

    private static function compilePattern($pattern) {
        $paramNames = [];

        $regex = preg_replace_callback('/\{([^}]+)\}/', function($matches) use (&$paramNames) {
            $paramNames[] = $matches[1];
            return '([^/]+)';
        }, $pattern);

        $regex = '/^' . str_replace('/', '\/', $regex) . '$/';

        return [
            'regex' => $regex,
            'params' => $paramNames
        ];
    }
}

class PrecompiledRouter {
    private $compiled;
    private $stats = [
        'static_hits' => 0,
        'dynamic_hits' => 0,
        'misses' => 0
    ];

    public function __construct($routes) {
        $this->compiled = RouteCompiler::compile($routes);
    }

    public function dispatch($method, $uri) {
        $path = parse_url($uri, PHP_URL_PATH);

        // 首先检查静态路由
        if (isset($this->compiled['static'][$method][$path])) {
            $this->stats['static_hits']++;
            return [
                'handler' => $this->compiled['static'][$method][$path]['handler'],
                'params' => []
            ];
        }

        // 检查动态路由
        if (isset($this->compiled['dynamic'][$method])) {
            foreach ($this->compiled['dynamic'][$method] as $route) {
                if (preg_match($route['regex']['regex'], $path, $matches)) {
                    array_shift($matches);

                    $params = [];
                    foreach ($route['regex']['params'] as $index => $name) {
                        $params[$name] = $matches[$index] ?? null;
                    }

                    $this->stats['dynamic_hits']++;
                    return [
                        'handler' => $route['handler'],
                        'params' => $params
                    ];
                }
            }
        }

        $this->stats['misses']++;
        return null;
    }

    public function getStats() {
        $total = array_sum($this->stats);
        return [
            'static_hits' => $this->stats['static_hits'],
            'dynamic_hits' => $this->stats['dynamic_hits'],
            'misses' => $this->stats['misses'],
            'total_requests' => $total,
            'static_ratio' => $total > 0 ? round($this->stats['static_hits'] / $total * 100, 2) : 0,
            'dynamic_ratio' => $total > 0 ? round($this->stats['dynamic_hits'] / $total * 100, 2) : 0
        ];
    }
}

// 定义路由
$routes = [
    ['method' => 'GET', 'pattern' => '/', 'handler' => 'homeHandler'],
    ['method' => 'GET', 'pattern' => '/about', 'handler' => 'aboutHandler'],
    ['method' => 'GET', 'pattern' => '/contact', 'handler' => 'contactHandler'],
    ['method' => 'GET', 'pattern' => '/users/{id}', 'handler' => 'userHandler'],
    ['method' => 'GET', 'pattern' => '/posts/{id}', 'handler' => 'postHandler'],
    ['method' => 'GET', 'pattern' => '/api/v1/users/{id}', 'handler' => 'apiUserHandler'],
    ['method' => 'POST', 'pattern' => '/api/v1/users', 'handler' => 'createUserHandler'],
];

$router = new PrecompiledRouter($routes);

// 处理函数
function homeHandler($request, $response, $params) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '首页', 'type' => 'static']));
}

function aboutHandler($request, $response, $params) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode(['message' => '关于页面', 'type' => 'static']));
}

function userHandler($request, $response, $params) {
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'message' => '用户详情',
        'type' => 'dynamic',
        'user_id' => $params['id']
    ]));
}

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use ($router) {
    $method = $request->server['request_method'];
    $uri = $request->server['request_uri'];

    if ($uri === '/router-stats') {
        $stats = $router->getStats();
        $response->header("Content-Type", "application/json");
        $response->end(json_encode($stats, JSON_PRETTY_PRINT));
        return;
    }

    $result = $router->dispatch($method, $uri);

    if ($result) {
        $handlerName = $result['handler'];
        if (function_exists($handlerName)) {
            $handlerName($request, $response, $result['params']);
        } else {
            $response->status(500);
            $response->end("Handler not found: {$handlerName}");
        }
    } else {
        $response->status(404);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Route not found']));
    }
});

echo "预编译路由服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "测试路由: /, /about, /users/123\n";
echo "查看统计: /router-stats\n";
$server->start();
?>
```

## 7.4 RESTful 路由

### 7.4.1 资源路由

```php
<?php
// restful_router.php

use Swoole\Http\Server;

class ResourceRouter {
    private $routes = [];

    public function resource($name, $controller) {
        $routes = [
            ['GET', "/{$name}", "{$controller}@index"],           // 列表
            ['GET', "/{$name}/create", "{$controller}@create"],   // 创建表单
            ['POST', "/{$name}", "{$controller}@store"],          // 保存
            ['GET', "/{$name}/{id}", "{$controller}@show"],       // 详情
            ['GET', "/{$name}/{id}/edit", "{$controller}@edit"],  // 编辑表单
            ['PUT', "/{$name}/{id}", "{$controller}@update"],     // 更新
            ['DELETE', "/{$name}/{id}", "{$controller}@destroy"], // 删除
        ];

        foreach ($routes as $route) {
            $this->routes[] = [
                'method' => $route[0],
                'pattern' => $route[1],
                'action' => $route[2]
            ];
        }
    }

    public function apiResource($name, $controller) {
        $routes = [
            ['GET', "/{$name}", "{$controller}@index"],
            ['POST', "/{$name}", "{$controller}@store"],
            ['GET', "/{$name}/{id}", "{$controller}@show"],
            ['PUT', "/{$name}/{id}", "{$controller}@update"],
            ['DELETE', "/{$name}/{id}", "{$controller}@destroy"],
        ];

        foreach ($routes as $route) {
            $this->routes[] = [
                'method' => $route[0],
                'pattern' => $route[1],
                'action' => $route[2]
            ];
        }
    }

    public function dispatch($method, $uri) {
        $path = parse_url($uri, PHP_URL_PATH);

        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            $params = $this->matchRoute($route['pattern'], $path);
            if ($params !== false) {
                return [
                    'action' => $route['action'],
                    'params' => $params
                ];
            }
        }

        return null;
    }

    private function matchRoute($pattern, $path) {
        $paramNames = [];

        $regex = preg_replace_callback('/\{([^}]+)\}/', function($matches) use (&$paramNames) {
            $paramNames[] = $matches[1];
            return '([^/]+)';
        }, $pattern);

        $regex = '/^' . str_replace('/', '\/', $regex) . '$/';

        if (preg_match($regex, $path, $matches)) {
            array_shift($matches);

            $params = [];
            foreach ($paramNames as $index => $name) {
                $params[$name] = $matches[$index] ?? null;
            }

            return $params;
        }

        return false;
    }

    public function getRoutes() {
        return $this->routes;
    }
}

// 控制器类
class UserController {
    public function index($request, $response, $params) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'index',
            'message' => '用户列表',
            'users' => [
                ['id' => 1, 'name' => '张三'],
                ['id' => 2, 'name' => '李四'],
            ]
        ]));
    }

    public function show($request, $response, $params) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'show',
            'message' => '用户详情',
            'user_id' => $params['id'],
            'user' => ['id' => $params['id'], 'name' => '用户' . $params['id']]
        ]));
    }

    public function store($request, $response, $params) {
        $data = $request->post ?? [];
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'store',
            'message' => '创建用户成功',
            'data' => $data
        ]));
    }

    public function update($request, $response, $params) {
        $data = $request->post ?? [];
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'update',
            'message' => '更新用户成功',
            'user_id' => $params['id'],
            'data' => $data
        ]));
    }

    public function destroy($request, $response, $params) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'destroy',
            'message' => '删除用户成功',
            'user_id' => $params['id']
        ]));
    }
}

class PostController {
    public function index($request, $response, $params) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'index',
            'message' => '文章列表',
            'posts' => [
                ['id' => 1, 'title' => '第一篇文章'],
                ['id' => 2, 'title' => '第二篇文章'],
            ]
        ]));
    }

    public function show($request, $response, $params) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'show',
            'message' => '文章详情',
            'post_id' => $params['id'],
            'post' => ['id' => $params['id'], 'title' => '文章' . $params['id']]
        ]));
    }

    public function store($request, $response, $params) {
        $data = $request->post ?? [];
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'store',
            'message' => '创建文章成功',
            'data' => $data
        ]));
    }

    public function update($request, $response, $params) {
        $data = $request->post ?? [];
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'update',
            'message' => '更新文章成功',
            'post_id' => $params['id'],
            'data' => $data
        ]));
    }

    public function destroy($request, $response, $params) {
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'action' => 'destroy',
            'message' => '删除文章成功',
            'post_id' => $params['id']
        ]));
    }
}

$router = new ResourceRouter();

// 注册资源路由
$router->apiResource('users', 'UserController');
$router->apiResource('posts', 'PostController');

// 添加路由列表查看
$router->routes[] = [
    'method' => 'GET',
    'pattern' => '/routes',
    'action' => 'showRoutes'
];

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use ($router) {
    $method = $request->server['request_method'];
    $uri = $request->server['request_uri'];

    if ($uri === '/routes') {
        $routes = $router->getRoutes();
        $response->header("Content-Type", "application/json");
        $response->end(json_encode($routes, JSON_PRETTY_PRINT));
        return;
    }

    $result = $router->dispatch($method, $uri);

    if ($result) {
        $action = $result['action'];
        $params = $result['params'];

        // 解析控制器和方法
        list($controllerName, $methodName) = explode('@', $action);

        if (class_exists($controllerName)) {
            $controller = new $controllerName();
            if (method_exists($controller, $methodName)) {
                $controller->$methodName($request, $response, $params);
            } else {
                $response->status(500);
                $response->end("Method {$methodName} not found in {$controllerName}");
            }
        } else {
            $response->status(500);
            $response->end("Controller {$controllerName} not found");
        }
    } else {
        $response->status(404);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => 'Route not found']));
    }
});

echo "RESTful 路由服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "可用路由:\n";
echo "  GET /routes - 查看所有路由\n";
echo "  GET /users - 用户列表\n";
echo "  GET /users/1 - 用户详情\n";
echo "  POST /users - 创建用户\n";
echo "  PUT /users/1 - 更新用户\n";
echo "  DELETE /users/1 - 删除用户\n";
echo "  GET /posts - 文章列表\n";
echo "  GET /posts/1 - 文章详情\n";
$server->start();
?>
```

## 本章练习

### 练习 1：路由中间件系统
实现一个完整的路由中间件系统：

```php
<?php
// middleware_router.php

class MiddlewareRouter {
    // 实现功能：
    // 1. 全局中间件
    // 2. 路由组中间件
    // 3. 单个路由中间件
    // 4. 中间件参数传递
    // 5. 中间件执行顺序控制
    // 6. 异常处理中间件
}

// 实现以下中间件：
// - 认证中间件
// - 权限检查中间件
// - 请求限流中间件
// - 日志记录中间件
// - CORS 中间件
?>
```

### 练习 2：动态路由加载
实现从配置文件动态加载路由：

```php
<?php
// dynamic_router.php

class ConfigRouter {
    // 实现功能：
    // 1. 从 JSON/YAML 文件加载路由
    // 2. 路由热重载
    // 3. 路由版本控制
    // 4. 路由依赖注入
    // 5. 路由缓存管理
}

// 配置文件示例 routes.json:
/*
{
    "routes": [
        {
            "method": "GET",
            "path": "/api/v1/users",
            "controller": "UserController@index",
            "middleware": ["auth", "throttle:60,1"]
        }
    ]
}
*/
?>
```

### 练习 3：路由性能基准测试
创建路由性能测试工具：

```php
<?php
// route_benchmark.php

class RouteBenchmark {
    // 实现功能：
    // 1. 不同路由数量的性能测试
    // 2. 静态 vs 动态路由性能对比
    // 3. 缓存效果测试
    // 4. 内存使用分析
    // 5. 生成性能报告
}
?>
```

### 练习 4：路由调试工具
开发路由调试和分析工具：

```php
<?php
// route_debugger.php

class RouteDebugger {
    // 实现功能：
    // 1. 路由匹配过程可视化
    // 2. 路由冲突检测
    // 3. 未使用路由检测
    // 4. 路由性能分析
    // 5. 路由文档生成
}
?>
```

## 本章小结

本章详细介绍了 Swoole 中路由系统的设计和实现，从基础的路由匹配到高级的性能优化，涵盖了现代 Web 应用路由系统的各个方面。

**关键要点：**

- **路由基础**：理解路由匹配原理和算法
- **高级特性**：中间件、路由组、命名路由
- **性能优化**：路由缓存、预编译、静态路由优先
- **RESTful 设计**：资源路由和控制器模式
- **可扩展性**：支持动态加载和热重载

**设计原则：**

1. **性能优先**：静态路由优于动态路由
2. **缓存策略**：合理使用路由缓存
3. **模块化设计**：中间件和控制器分离
4. **可维护性**：清晰的路由组织结构
5. **扩展性**：支持插件和自定义功能

**下一章预告：**
下一章我们将学习中间件机制，了解如何在请求处理过程中插入自定义逻辑，实现认证、日志、缓存等功能。
