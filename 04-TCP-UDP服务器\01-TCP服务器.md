# 第13章：TCP 服务器开发

## 学习目标
- 理解 TCP 协议的特点和应用场景
- 掌握 Swoole TCP 服务器的创建和配置
- 学会处理 TCP 连接和数据传输
- 了解 TCP 服务器的性能优化

## 13.1 TCP 协议基础

### 13.1.1 TCP 协议特点

TCP (Transmission Control Protocol) 是一种面向连接的、可靠的传输层协议。它具有以下特点：

- **面向连接**：通信前需要建立连接
- **可靠传输**：保证数据完整性和顺序
- **流量控制**：防止发送方发送过快
- **拥塞控制**：避免网络拥塞
- **全双工通信**：双向数据传输

```php
<?php
// tcp_basic_server.php

use Swoole\Server;

class BasicTCPServer {
    private $server;
    private $connections = [];
    private $stats = [
        'total_connections' => 0,
        'current_connections' => 0,
        'bytes_received' => 0,
        'bytes_sent' => 0,
        'start_time' => 0
    ];
    
    public function __construct($host = '0.0.0.0', $port = 9501) {
        $this->server = new Server($host, $port, SWOOLE_PROCESS, SWOOLE_SOCK_TCP);
        $this->setupServer();
        $this->setupEvents();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 4,
            'max_conn' => 10000,
            'heartbeat_check_interval' => 60,
            'heartbeat_idle_time' => 600,
            'open_length_check' => true,
            'package_length_type' => 'N',
            'package_length_offset' => 0,
            'package_body_offset' => 4,
            'package_max_length' => 2000000,
        ]);
    }
    
    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('connect', [$this, 'onConnect']);
        $this->server->on('receive', [$this, 'onReceive']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
    }
    
    public function onStart($server) {
        $this->stats['start_time'] = time();
        echo "TCP 服务器启动成功\n";
        echo "监听地址: {$server->host}:{$server->port}\n";
        echo "Master PID: {$server->master_pid}\n";
        echo "Manager PID: {$server->manager_pid}\n";
    }
    
    public function onWorkerStart($server, $workerId) {
        echo "Worker #{$workerId} 启动，PID: " . getmypid() . "\n";
    }
    
    public function onConnect($server, $fd, $reactorId) {
        $this->stats['total_connections']++;
        $this->stats['current_connections']++;
        
        // 获取连接信息
        $clientInfo = $server->getClientInfo($fd);
        
        $this->connections[$fd] = [
            'fd' => $fd,
            'remote_ip' => $clientInfo['remote_ip'],
            'remote_port' => $clientInfo['remote_port'],
            'connect_time' => time(),
            'last_active' => time(),
            'bytes_received' => 0,
            'bytes_sent' => 0,
        ];
        
        echo "新连接: FD={$fd}, IP={$clientInfo['remote_ip']}:{$clientInfo['remote_port']}\n";
        
        // 发送欢迎消息
        $welcome = "欢迎连接到 TCP 服务器！连接ID: {$fd}\n";
        $packet = pack('N', strlen($welcome)) . $welcome;
        $server->send($fd, $packet);
        
        $this->stats['bytes_sent'] += strlen($packet);
        $this->connections[$fd]['bytes_sent'] += strlen($packet);
    }
    
    public function onReceive($server, $fd, $reactorId, $data) {
        $this->stats['bytes_received'] += strlen($data);
        
        if (isset($this->connections[$fd])) {
            $this->connections[$fd]['last_active'] = time();
            $this->connections[$fd]['bytes_received'] += strlen($data);
        }
        
        // 解析数据包
        $length = unpack('N', substr($data, 0, 4))[1];
        $message = substr($data, 4, $length);
        
        echo "收到数据: FD={$fd}, 长度={$length}, 内容={$message}\n";
        
        // 处理消息
        $this->handleMessage($server, $fd, $message);
    }
    
    public function onClose($server, $fd, $reactorId) {
        $this->stats['current_connections']--;
        
        if (isset($this->connections[$fd])) {
            $connection = $this->connections[$fd];
            $duration = time() - $connection['connect_time'];
            
            echo "连接关闭: FD={$fd}, 持续时间={$duration}秒, " .
                 "接收={$connection['bytes_received']}字节, " .
                 "发送={$connection['bytes_sent']}字节\n";
            
            unset($this->connections[$fd]);
        }
    }
    
    private function handleMessage($server, $fd, $message) {
        $message = trim($message);
        
        // 解析命令
        $parts = explode(' ', $message, 2);
        $command = strtoupper($parts[0]);
        $args = $parts[1] ?? '';
        
        switch ($command) {
            case 'PING':
                $this->handlePing($server, $fd);
                break;
            case 'ECHO':
                $this->handleEcho($server, $fd, $args);
                break;
            case 'TIME':
                $this->handleTime($server, $fd);
                break;
            case 'STATS':
                $this->handleStats($server, $fd);
                break;
            case 'LIST':
                $this->handleList($server, $fd);
                break;
            case 'BROADCAST':
                $this->handleBroadcast($server, $fd, $args);
                break;
            case 'QUIT':
                $this->handleQuit($server, $fd);
                break;
            default:
                $this->sendResponse($server, $fd, "未知命令: {$command}");
                break;
        }
    }
    
    private function handlePing($server, $fd) {
        $this->sendResponse($server, $fd, "PONG");
    }
    
    private function handleEcho($server, $fd, $message) {
        $this->sendResponse($server, $fd, "ECHO: {$message}");
    }
    
    private function handleTime($server, $fd) {
        $time = date('Y-m-d H:i:s');
        $this->sendResponse($server, $fd, "当前时间: {$time}");
    }
    
    private function handleStats($server, $fd) {
        $uptime = time() - $this->stats['start_time'];
        $stats = [
            "服务器运行时间: {$uptime} 秒",
            "总连接数: {$this->stats['total_connections']}",
            "当前连接数: {$this->stats['current_connections']}",
            "接收字节数: {$this->stats['bytes_received']}",
            "发送字节数: {$this->stats['bytes_sent']}",
            "内存使用: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB"
        ];
        
        $this->sendResponse($server, $fd, implode("\n", $stats));
    }
    
    private function handleList($server, $fd) {
        $list = ["在线连接列表:"];
        
        foreach ($this->connections as $connFd => $conn) {
            $duration = time() - $conn['connect_time'];
            $list[] = "FD={$connFd}, IP={$conn['remote_ip']}, 在线={$duration}秒";
        }
        
        $this->sendResponse($server, $fd, implode("\n", $list));
    }
    
    private function handleBroadcast($server, $fd, $message) {
        if (empty($message)) {
            $this->sendResponse($server, $fd, "广播消息不能为空");
            return;
        }
        
        $senderInfo = $this->connections[$fd] ?? null;
        $senderIp = $senderInfo ? $senderInfo['remote_ip'] : 'unknown';
        
        $broadcastMsg = "广播消息 [来自 {$senderIp}]: {$message}";
        $count = 0;
        
        foreach ($server->connections as $connFd) {
            if ($connFd !== $fd && $server->isEstablished($connFd)) {
                $this->sendResponse($server, $connFd, $broadcastMsg);
                $count++;
            }
        }
        
        $this->sendResponse($server, $fd, "广播完成，发送给 {$count} 个连接");
    }
    
    private function handleQuit($server, $fd) {
        $this->sendResponse($server, $fd, "再见！");
        $server->close($fd);
    }
    
    private function sendResponse($server, $fd, $message) {
        $packet = pack('N', strlen($message)) . $message;
        
        if ($server->send($fd, $packet)) {
            $this->stats['bytes_sent'] += strlen($packet);
            
            if (isset($this->connections[$fd])) {
                $this->connections[$fd]['bytes_sent'] += strlen($packet);
            }
        }
    }
    
    public function start() {
        $this->server->start();
    }
}

$server = new BasicTCPServer('0.0.0.0', 9501);
$server->start();
?>
```

### 13.1.2 TCP 客户端测试工具

```php
<?php
// tcp_client.php

class TCPClient {
    private $socket;
    private $host;
    private $port;
    private $connected = false;
    
    public function __construct($host = '127.0.0.1', $port = 9501) {
        $this->host = $host;
        $this->port = $port;
    }
    
    public function connect() {
        $this->socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        
        if (!$this->socket) {
            throw new Exception("无法创建 socket: " . socket_strerror(socket_last_error()));
        }
        
        $result = socket_connect($this->socket, $this->host, $this->port);
        
        if (!$result) {
            throw new Exception("连接失败: " . socket_strerror(socket_last_error($this->socket)));
        }
        
        $this->connected = true;
        echo "已连接到 {$this->host}:{$this->port}\n";
        
        // 接收欢迎消息
        $welcome = $this->receive();
        echo "服务器: {$welcome}\n";
    }
    
    public function send($message) {
        if (!$this->connected) {
            throw new Exception("未连接到服务器");
        }
        
        $packet = pack('N', strlen($message)) . $message;
        $result = socket_write($this->socket, $packet, strlen($packet));
        
        if ($result === false) {
            throw new Exception("发送失败: " . socket_strerror(socket_last_error($this->socket)));
        }
        
        return $result;
    }
    
    public function receive() {
        if (!$this->connected) {
            throw new Exception("未连接到服务器");
        }
        
        // 读取长度
        $lengthData = socket_read($this->socket, 4);
        if ($lengthData === false || strlen($lengthData) !== 4) {
            throw new Exception("读取长度失败");
        }
        
        $length = unpack('N', $lengthData)[1];
        
        // 读取消息内容
        $message = socket_read($this->socket, $length);
        if ($message === false) {
            throw new Exception("读取消息失败: " . socket_strerror(socket_last_error($this->socket)));
        }
        
        return $message;
    }
    
    public function close() {
        if ($this->connected) {
            socket_close($this->socket);
            $this->connected = false;
            echo "连接已关闭\n";
        }
    }
    
    public function isConnected() {
        return $this->connected;
    }
}

// 交互式客户端
function runInteractiveClient() {
    $client = new TCPClient();
    
    try {
        $client->connect();
        
        echo "\n可用命令:\n";
        echo "  PING - 测试连接\n";
        echo "  ECHO <message> - 回显消息\n";
        echo "  TIME - 获取服务器时间\n";
        echo "  STATS - 获取服务器统计\n";
        echo "  LIST - 获取在线连接列表\n";
        echo "  BROADCAST <message> - 广播消息\n";
        echo "  QUIT - 退出\n\n";
        
        while ($client->isConnected()) {
            echo "请输入命令: ";
            $input = trim(fgets(STDIN));
            
            if (empty($input)) {
                continue;
            }
            
            if (strtoupper($input) === 'EXIT') {
                break;
            }
            
            try {
                $client->send($input);
                $response = $client->receive();
                echo "服务器响应: {$response}\n\n";
                
                if (strtoupper($input) === 'QUIT') {
                    break;
                }
            } catch (Exception $e) {
                echo "错误: " . $e->getMessage() . "\n";
                break;
            }
        }
        
    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
    } finally {
        $client->close();
    }
}

// 批量测试客户端
function runBatchTest() {
    $commands = ['PING', 'TIME', 'ECHO Hello World', 'STATS'];
    
    for ($i = 1; $i <= 3; $i++) {
        echo "=== 客户端 {$i} ===\n";
        
        $client = new TCPClient();
        
        try {
            $client->connect();
            
            foreach ($commands as $command) {
                echo "发送: {$command}\n";
                $client->send($command);
                $response = $client->receive();
                echo "响应: {$response}\n\n";
                
                usleep(500000); // 0.5秒延迟
            }
            
            $client->send('QUIT');
            $client->receive();
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        } finally {
            $client->close();
        }
        
        echo "\n";
    }
}

// 根据命令行参数选择运行模式
if ($argc > 1 && $argv[1] === 'batch') {
    runBatchTest();
} else {
    runInteractiveClient();
}
?>
```

## 13.2 高级 TCP 服务器

### 13.2.1 协议设计和数据包处理

```php
<?php
// advanced_tcp_server.php

use Swoole\Server;

// 自定义协议类
class Protocol {
    const HEADER_SIZE = 8;
    const VERSION = 1;
    
    // 消息类型
    const TYPE_HEARTBEAT = 1;
    const TYPE_AUTH = 2;
    const TYPE_MESSAGE = 3;
    const TYPE_RESPONSE = 4;
    const TYPE_ERROR = 5;
    
    public static function pack($type, $data = '') {
        $dataLength = strlen($data);
        $totalLength = self::HEADER_SIZE + $dataLength;
        
        return pack('NnCC', $totalLength, $type, self::VERSION, 0) . $data;
    }
    
    public static function unpack($buffer) {
        if (strlen($buffer) < self::HEADER_SIZE) {
            return null;
        }
        
        $header = unpack('NtotalLength/ntype/Cversion/Creserved', substr($buffer, 0, self::HEADER_SIZE));
        
        if (strlen($buffer) < $header['totalLength']) {
            return null; // 数据包不完整
        }
        
        $data = substr($buffer, self::HEADER_SIZE, $header['totalLength'] - self::HEADER_SIZE);
        
        return [
            'type' => $header['type'],
            'version' => $header['version'],
            'data' => $data,
            'total_length' => $header['totalLength']
        ];
    }
}

class AdvancedTCPServer {
    private $server;
    private $connections = [];
    private $authenticatedUsers = [];
    private $messageHandlers = [];
    
    public function __construct($host = '0.0.0.0', $port = 9502) {
        $this->server = new Server($host, $port, SWOOLE_PROCESS, SWOOLE_SOCK_TCP);
        $this->setupServer();
        $this->setupEvents();
        $this->setupMessageHandlers();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 4,
            'max_conn' => 10000,
            'heartbeat_check_interval' => 30,
            'heartbeat_idle_time' => 60,
            'open_length_check' => true,
            'package_length_type' => 'N',
            'package_length_offset' => 0,
            'package_body_offset' => 0,
            'package_max_length' => 2000000,
        ]);
    }
    
    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('connect', [$this, 'onConnect']);
        $this->server->on('receive', [$this, 'onReceive']);
        $this->server->on('close', [$this, 'onClose']);
    }
    
    private function setupMessageHandlers() {
        $this->messageHandlers = [
            Protocol::TYPE_HEARTBEAT => [$this, 'handleHeartbeat'],
            Protocol::TYPE_AUTH => [$this, 'handleAuth'],
            Protocol::TYPE_MESSAGE => [$this, 'handleMessage'],
        ];
    }
    
    public function onStart($server) {
        echo "高级 TCP 服务器启动成功，监听 {$server->host}:{$server->port}\n";
    }
    
    public function onConnect($server, $fd, $reactorId) {
        $clientInfo = $server->getClientInfo($fd);
        
        $this->connections[$fd] = [
            'fd' => $fd,
            'ip' => $clientInfo['remote_ip'],
            'port' => $clientInfo['remote_port'],
            'connect_time' => time(),
            'last_heartbeat' => time(),
            'authenticated' => false,
            'username' => '',
            'buffer' => '',
        ];
        
        echo "新连接: FD={$fd}, IP={$clientInfo['remote_ip']}\n";
    }
    
    public function onReceive($server, $fd, $reactorId, $data) {
        if (!isset($this->connections[$fd])) {
            return;
        }
        
        // 将数据添加到缓冲区
        $this->connections[$fd]['buffer'] .= $data;
        
        // 处理缓冲区中的完整数据包
        $this->processBuffer($server, $fd);
    }
    
    public function onClose($server, $fd, $reactorId) {
        if (isset($this->connections[$fd])) {
            $connection = $this->connections[$fd];
            
            if ($connection['authenticated']) {
                unset($this->authenticatedUsers[$connection['username']]);
                echo "用户 {$connection['username']} 断开连接\n";
            } else {
                echo "未认证连接 FD={$fd} 断开\n";
            }
            
            unset($this->connections[$fd]);
        }
    }
    
    private function processBuffer($server, $fd) {
        $buffer = &$this->connections[$fd]['buffer'];
        
        while (strlen($buffer) >= Protocol::HEADER_SIZE) {
            $packet = Protocol::unpack($buffer);
            
            if ($packet === null) {
                break; // 数据包不完整，等待更多数据
            }
            
            // 从缓冲区移除已处理的数据
            $buffer = substr($buffer, $packet['total_length']);
            
            // 处理数据包
            $this->handlePacket($server, $fd, $packet);
        }
    }
    
    private function handlePacket($server, $fd, $packet) {
        $type = $packet['type'];
        
        if (isset($this->messageHandlers[$type])) {
            $handler = $this->messageHandlers[$type];
            call_user_func($handler, $server, $fd, $packet['data']);
        } else {
            $this->sendError($server, $fd, "未知的消息类型: {$type}");
        }
    }
    
    private function handleHeartbeat($server, $fd, $data) {
        $this->connections[$fd]['last_heartbeat'] = time();
        
        // 发送心跳响应
        $response = Protocol::pack(Protocol::TYPE_HEARTBEAT, 'pong');
        $server->send($fd, $response);
    }
    
    private function handleAuth($server, $fd, $data) {
        $authData = json_decode($data, true);
        
        if (!$authData || !isset($authData['username']) || !isset($authData['password'])) {
            $this->sendError($server, $fd, "认证数据格式错误");
            return;
        }
        
        $username = $authData['username'];
        $password = $authData['password'];
        
        // 简单的认证逻辑
        if ($this->authenticate($username, $password)) {
            if (isset($this->authenticatedUsers[$username])) {
                $this->sendError($server, $fd, "用户已在线");
                return;
            }
            
            $this->connections[$fd]['authenticated'] = true;
            $this->connections[$fd]['username'] = $username;
            $this->authenticatedUsers[$username] = $fd;
            
            $response = Protocol::pack(Protocol::TYPE_RESPONSE, json_encode([
                'status' => 'success',
                'message' => '认证成功',
                'username' => $username
            ]));
            
            $server->send($fd, $response);
            
            echo "用户 {$username} 认证成功\n";
        } else {
            $this->sendError($server, $fd, "认证失败");
        }
    }
    
    private function handleMessage($server, $fd, $data) {
        if (!$this->connections[$fd]['authenticated']) {
            $this->sendError($server, $fd, "请先认证");
            return;
        }
        
        $messageData = json_decode($data, true);
        
        if (!$messageData || !isset($messageData['type'])) {
            $this->sendError($server, $fd, "消息格式错误");
            return;
        }
        
        switch ($messageData['type']) {
            case 'broadcast':
                $this->handleBroadcast($server, $fd, $messageData);
                break;
            case 'private':
                $this->handlePrivateMessage($server, $fd, $messageData);
                break;
            case 'get_users':
                $this->handleGetUsers($server, $fd);
                break;
            default:
                $this->sendError($server, $fd, "未知的消息类型");
                break;
        }
    }
    
    private function handleBroadcast($server, $fd, $messageData) {
        $username = $this->connections[$fd]['username'];
        $message = $messageData['message'] ?? '';
        
        if (empty($message)) {
            $this->sendError($server, $fd, "消息内容不能为空");
            return;
        }
        
        $broadcastData = [
            'type' => 'broadcast',
            'from' => $username,
            'message' => $message,
            'timestamp' => time()
        ];
        
        $packet = Protocol::pack(Protocol::TYPE_MESSAGE, json_encode($broadcastData));
        
        foreach ($this->authenticatedUsers as $user => $userFd) {
            if ($userFd !== $fd && $server->isEstablished($userFd)) {
                $server->send($userFd, $packet);
            }
        }
        
        // 发送确认给发送者
        $response = Protocol::pack(Protocol::TYPE_RESPONSE, json_encode([
            'status' => 'success',
            'message' => '广播发送成功'
        ]));
        
        $server->send($fd, $response);
        
        echo "广播消息: {$username} -> {$message}\n";
    }
    
    private function handlePrivateMessage($server, $fd, $messageData) {
        $username = $this->connections[$fd]['username'];
        $targetUser = $messageData['target'] ?? '';
        $message = $messageData['message'] ?? '';
        
        if (empty($targetUser) || empty($message)) {
            $this->sendError($server, $fd, "目标用户和消息内容不能为空");
            return;
        }
        
        if (!isset($this->authenticatedUsers[$targetUser])) {
            $this->sendError($server, $fd, "目标用户不在线");
            return;
        }
        
        $targetFd = $this->authenticatedUsers[$targetUser];
        
        $privateData = [
            'type' => 'private',
            'from' => $username,
            'message' => $message,
            'timestamp' => time()
        ];
        
        $packet = Protocol::pack(Protocol::TYPE_MESSAGE, json_encode($privateData));
        $server->send($targetFd, $packet);
        
        // 发送确认给发送者
        $response = Protocol::pack(Protocol::TYPE_RESPONSE, json_encode([
            'status' => 'success',
            'message' => "私信已发送给 {$targetUser}"
        ]));
        
        $server->send($fd, $response);
        
        echo "私信: {$username} -> {$targetUser}: {$message}\n";
    }
    
    private function handleGetUsers($server, $fd) {
        $users = array_keys($this->authenticatedUsers);
        
        $response = Protocol::pack(Protocol::TYPE_RESPONSE, json_encode([
            'status' => 'success',
            'data' => $users
        ]));
        
        $server->send($fd, $response);
    }
    
    private function authenticate($username, $password) {
        // 简单的认证逻辑，实际应用中应该查询数据库
        $validUsers = [
            'admin' => 'admin123',
            'user1' => 'password1',
            'user2' => 'password2',
        ];
        
        return isset($validUsers[$username]) && $validUsers[$username] === $password;
    }
    
    private function sendError($server, $fd, $message) {
        $errorData = [
            'status' => 'error',
            'message' => $message
        ];
        
        $packet = Protocol::pack(Protocol::TYPE_ERROR, json_encode($errorData));
        $server->send($fd, $packet);
    }
    
    public function start() {
        $this->server->start();
    }
}

$server = new AdvancedTCPServer('0.0.0.0', 9502);
$server->start();
?>
```

### 13.2.2 高级客户端

```php
<?php
// advanced_tcp_client.php

class AdvancedTCPClient {
    private $socket;
    private $host;
    private $port;
    private $connected = false;
    private $authenticated = false;
    private $username = '';
    private $buffer = '';

    public function __construct($host = '127.0.0.1', $port = 9502) {
        $this->host = $host;
        $this->port = $port;
    }

    public function connect() {
        $this->socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);

        if (!$this->socket) {
            throw new Exception("无法创建 socket");
        }

        socket_set_option($this->socket, SOL_SOCKET, SO_RCVTIMEO, ['sec' => 5, 'usec' => 0]);

        $result = socket_connect($this->socket, $this->host, $this->port);

        if (!$result) {
            throw new Exception("连接失败");
        }

        $this->connected = true;
        echo "已连接到 {$this->host}:{$this->port}\n";
    }

    public function authenticate($username, $password) {
        if (!$this->connected) {
            throw new Exception("未连接到服务器");
        }

        $authData = json_encode([
            'username' => $username,
            'password' => $password
        ]);

        $packet = Protocol::pack(Protocol::TYPE_AUTH, $authData);
        $this->sendRaw($packet);

        $response = $this->receivePacket();

        if ($response && $response['type'] === Protocol::TYPE_RESPONSE) {
            $data = json_decode($response['data'], true);

            if ($data['status'] === 'success') {
                $this->authenticated = true;
                $this->username = $username;
                echo "认证成功: {$username}\n";
                return true;
            } else {
                echo "认证失败: {$data['message']}\n";
                return false;
            }
        } elseif ($response && $response['type'] === Protocol::TYPE_ERROR) {
            $data = json_decode($response['data'], true);
            echo "认证错误: {$data['message']}\n";
            return false;
        }

        return false;
    }

    public function sendHeartbeat() {
        if (!$this->connected) {
            return false;
        }

        $packet = Protocol::pack(Protocol::TYPE_HEARTBEAT, 'ping');
        $this->sendRaw($packet);

        $response = $this->receivePacket();

        return $response && $response['type'] === Protocol::TYPE_HEARTBEAT;
    }

    public function sendBroadcast($message) {
        if (!$this->authenticated) {
            throw new Exception("请先认证");
        }

        $messageData = json_encode([
            'type' => 'broadcast',
            'message' => $message
        ]);

        $packet = Protocol::pack(Protocol::TYPE_MESSAGE, $messageData);
        $this->sendRaw($packet);

        $response = $this->receivePacket();

        if ($response && $response['type'] === Protocol::TYPE_RESPONSE) {
            $data = json_decode($response['data'], true);
            return $data['status'] === 'success';
        }

        return false;
    }

    public function sendPrivateMessage($target, $message) {
        if (!$this->authenticated) {
            throw new Exception("请先认证");
        }

        $messageData = json_encode([
            'type' => 'private',
            'target' => $target,
            'message' => $message
        ]);

        $packet = Protocol::pack(Protocol::TYPE_MESSAGE, $messageData);
        $this->sendRaw($packet);

        $response = $this->receivePacket();

        if ($response && $response['type'] === Protocol::TYPE_RESPONSE) {
            $data = json_decode($response['data'], true);
            return $data['status'] === 'success';
        }

        return false;
    }

    public function getOnlineUsers() {
        if (!$this->authenticated) {
            throw new Exception("请先认证");
        }

        $messageData = json_encode(['type' => 'get_users']);
        $packet = Protocol::pack(Protocol::TYPE_MESSAGE, $messageData);
        $this->sendRaw($packet);

        $response = $this->receivePacket();

        if ($response && $response['type'] === Protocol::TYPE_RESPONSE) {
            $data = json_decode($response['data'], true);

            if ($data['status'] === 'success') {
                return $data['data'];
            }
        }

        return [];
    }

    public function receiveMessage() {
        $packet = $this->receivePacket();

        if (!$packet) {
            return null;
        }

        switch ($packet['type']) {
            case Protocol::TYPE_MESSAGE:
                return json_decode($packet['data'], true);
            case Protocol::TYPE_ERROR:
                $error = json_decode($packet['data'], true);
                throw new Exception("服务器错误: " . $error['message']);
            default:
                return null;
        }
    }

    private function sendRaw($data) {
        $result = socket_write($this->socket, $data, strlen($data));

        if ($result === false) {
            throw new Exception("发送失败");
        }

        return $result;
    }

    private function receivePacket() {
        // 读取数据到缓冲区
        $data = socket_read($this->socket, 8192);

        if ($data === false) {
            return null;
        }

        $this->buffer .= $data;

        // 尝试解析数据包
        if (strlen($this->buffer) >= Protocol::HEADER_SIZE) {
            $packet = Protocol::unpack($this->buffer);

            if ($packet !== null) {
                // 从缓冲区移除已处理的数据
                $this->buffer = substr($this->buffer, $packet['total_length']);
                return $packet;
            }
        }

        return null;
    }

    public function close() {
        if ($this->connected) {
            socket_close($this->socket);
            $this->connected = false;
            $this->authenticated = false;
            echo "连接已关闭\n";
        }
    }

    public function isConnected() {
        return $this->connected;
    }

    public function isAuthenticated() {
        return $this->authenticated;
    }

    public function getUsername() {
        return $this->username;
    }
}

// 交互式高级客户端
function runAdvancedClient() {
    $client = new AdvancedTCPClient();

    try {
        $client->connect();

        // 认证
        echo "请输入用户名: ";
        $username = trim(fgets(STDIN));
        echo "请输入密码: ";
        $password = trim(fgets(STDIN));

        if (!$client->authenticate($username, $password)) {
            return;
        }

        echo "\n可用命令:\n";
        echo "  /broadcast <message> - 广播消息\n";
        echo "  /private <user> <message> - 私信\n";
        echo "  /users - 获取在线用户列表\n";
        echo "  /ping - 发送心跳\n";
        echo "  /quit - 退出\n\n";

        // 启动消息接收线程（简化版）
        $pid = pcntl_fork();

        if ($pid == 0) {
            // 子进程：接收消息
            while ($client->isConnected()) {
                try {
                    $message = $client->receiveMessage();

                    if ($message) {
                        switch ($message['type']) {
                            case 'broadcast':
                                echo "\n[广播] {$message['from']}: {$message['message']}\n";
                                break;
                            case 'private':
                                echo "\n[私信] {$message['from']}: {$message['message']}\n";
                                break;
                        }
                        echo "请输入命令: ";
                    }
                } catch (Exception $e) {
                    break;
                }

                usleep(100000); // 0.1秒
            }
            exit(0);
        } else {
            // 父进程：发送命令
            while ($client->isConnected()) {
                echo "请输入命令: ";
                $input = trim(fgets(STDIN));

                if (empty($input)) {
                    continue;
                }

                $parts = explode(' ', $input, 3);
                $command = $parts[0];

                try {
                    switch ($command) {
                        case '/broadcast':
                            if (isset($parts[1])) {
                                $message = implode(' ', array_slice($parts, 1));
                                if ($client->sendBroadcast($message)) {
                                    echo "广播发送成功\n";
                                }
                            } else {
                                echo "用法: /broadcast <message>\n";
                            }
                            break;

                        case '/private':
                            if (isset($parts[1]) && isset($parts[2])) {
                                if ($client->sendPrivateMessage($parts[1], $parts[2])) {
                                    echo "私信发送成功\n";
                                }
                            } else {
                                echo "用法: /private <user> <message>\n";
                            }
                            break;

                        case '/users':
                            $users = $client->getOnlineUsers();
                            echo "在线用户: " . implode(', ', $users) . "\n";
                            break;

                        case '/ping':
                            if ($client->sendHeartbeat()) {
                                echo "心跳成功\n";
                            } else {
                                echo "心跳失败\n";
                            }
                            break;

                        case '/quit':
                            break 2;

                        default:
                            echo "未知命令: {$command}\n";
                            break;
                    }
                } catch (Exception $e) {
                    echo "错误: " . $e->getMessage() . "\n";
                }
            }

            // 终止子进程
            posix_kill($pid, SIGTERM);
            pcntl_wait($status);
        }

    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
    } finally {
        $client->close();
    }
}

if (php_sapi_name() === 'cli') {
    runAdvancedClient();
}
?>
```

## 本章练习

### 练习 1：文件传输服务器
实现一个基于 TCP 的文件传输服务器：

```php
<?php
// file_transfer_server.php

class FileTransferServer {
    // 实现功能：
    // 1. 文件上传和下载
    // 2. 断点续传
    // 3. 文件完整性校验
    // 4. 传输进度监控
    // 5. 并发传输控制
    // 6. 文件权限管理
}
?>
```

### 练习 2：TCP 代理服务器
创建一个 TCP 代理服务器：

```php
<?php
// tcp_proxy_server.php

class TCPProxyServer {
    // 实现功能：
    // 1. 透明代理
    // 2. 负载均衡
    // 3. 连接池管理
    // 4. 流量统计
    // 5. 访问控制
    // 6. 故障转移
}
?>
```

### 练习 3：TCP 性能测试工具
开发 TCP 服务器性能测试工具：

```php
<?php
// tcp_benchmark.php

class TCPBenchmark {
    // 实现功能：
    // 1. 并发连接测试
    // 2. 吞吐量测试
    // 3. 延迟测试
    // 4. 压力测试
    // 5. 性能报告生成
    // 6. 资源使用监控
}
?>
```

### 练习 4：TCP 连接池
实现 TCP 连接池管理：

```php
<?php
// tcp_connection_pool.php

class TCPConnectionPool {
    // 实现功能：
    // 1. 连接复用
    // 2. 连接健康检查
    // 3. 动态扩缩容
    // 4. 连接超时处理
    // 5. 负载均衡
    // 6. 监控统计
}
?>
```

## 本章小结

本章详细介绍了 Swoole TCP 服务器的开发，从基础概念到高级应用，涵盖了 TCP 服务器开发的各个方面。

**关键要点：**

- **TCP 特性**：面向连接、可靠传输、流量控制
- **协议设计**：自定义协议、数据包处理、缓冲区管理
- **连接管理**：连接状态跟踪、认证授权、心跳检测
- **消息处理**：消息路由、广播、私信
- **性能优化**：连接池、异步处理、内存管理

**设计原则：**

1. **协议设计**：简单、高效、可扩展
2. **状态管理**：清晰的连接状态跟踪
3. **错误处理**：完善的异常处理机制
4. **安全考虑**：认证、授权、数据验证
5. **性能优化**：合理的资源使用和并发控制

**最佳实践：**

1. 设计清晰的通信协议
2. 实现完善的连接管理
3. 使用缓冲区处理数据包
4. 添加心跳检测机制
5. 实现适当的错误处理

**下一章预告：**
下一章我们将学习 UDP 服务器开发，了解无连接协议的特点和应用场景。
