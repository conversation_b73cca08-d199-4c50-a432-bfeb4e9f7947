# 第6章：请求与响应处理

## 学习目标
- 深入理解 HTTP 请求的解析和处理
- 掌握各种响应类型的生成方法
- 学会处理文件上传和下载
- 了解流式处理和分块传输

## 6.1 HTTP 请求解析

### 6.1.1 请求行和头部解析

```php
<?php
// request_parser.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 解析请求行
    $requestLine = [
        'method' => $request->server['request_method'],
        'uri' => $request->server['request_uri'],
        'protocol' => $request->server['server_protocol'],
        'query_string' => $request->server['query_string'] ?? '',
    ];
    
    // 解析 URI 组件
    $uriParts = parse_url($request->server['request_uri']);
    
    // 解析请求头
    $headers = [];
    foreach ($request->header as $key => $value) {
        $headers[ucwords($key, '-')] = $value;
    }
    
    // 分析常用头部
    $headerAnalysis = [
        'content_type' => $request->header['content-type'] ?? 'unknown',
        'content_length' => $request->header['content-length'] ?? 0,
        'user_agent' => $request->header['user-agent'] ?? 'unknown',
        'accept' => $request->header['accept'] ?? '*/*',
        'accept_encoding' => $request->header['accept-encoding'] ?? 'none',
        'accept_language' => $request->header['accept-language'] ?? 'unknown',
        'connection' => $request->header['connection'] ?? 'close',
        'host' => $request->header['host'] ?? 'unknown',
    ];
    
    // 检测客户端能力
    $clientCapabilities = [
        'supports_gzip' => strpos($headerAnalysis['accept_encoding'], 'gzip') !== false,
        'supports_deflate' => strpos($headerAnalysis['accept_encoding'], 'deflate') !== false,
        'supports_br' => strpos($headerAnalysis['accept_encoding'], 'br') !== false,
        'keep_alive' => strtolower($headerAnalysis['connection']) === 'keep-alive',
        'accepts_json' => strpos($headerAnalysis['accept'], 'application/json') !== false,
        'accepts_html' => strpos($headerAnalysis['accept'], 'text/html') !== false,
    ];
    
    $result = [
        'request_line' => $requestLine,
        'uri_parts' => $uriParts,
        'headers' => $headers,
        'header_analysis' => $headerAnalysis,
        'client_capabilities' => $clientCapabilities,
        'raw_headers' => $request->header,
    ];
    
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->end(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
});

echo "请求解析服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

### 6.1.2 参数处理和验证

```php
<?php
// parameter_handler.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];
    $method = $request->server['request_method'];
    
    if ($uri === '/validate' && $method === 'POST') {
        handleValidation($request, $response);
    } elseif ($uri === '/form') {
        showForm($request, $response);
    } else {
        showParameterInfo($request, $response);
    }
});

function handleValidation($request, $response) {
    // 定义验证规则
    $rules = [
        'name' => ['required', 'string', 'min:2', 'max:50'],
        'email' => ['required', 'email'],
        'age' => ['required', 'integer', 'min:1', 'max:120'],
        'website' => ['url'],
        'phone' => ['regex:/^1[3-9]\d{9}$/'],
    ];
    
    // 获取参数
    $params = array_merge($request->get ?? [], $request->post ?? []);
    
    // 验证参数
    $errors = [];
    $validated = [];
    
    foreach ($rules as $field => $fieldRules) {
        $value = $params[$field] ?? null;
        $fieldErrors = validateField($field, $value, $fieldRules);
        
        if (!empty($fieldErrors)) {
            $errors[$field] = $fieldErrors;
        } else {
            $validated[$field] = $value;
        }
    }
    
    $result = [
        'success' => empty($errors),
        'validated_data' => $validated,
        'errors' => $errors,
        'raw_input' => $params,
    ];
    
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->end(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function validateField($field, $value, $rules) {
    $errors = [];
    
    foreach ($rules as $rule) {
        if (is_string($rule)) {
            $ruleParts = explode(':', $rule);
            $ruleName = $ruleParts[0];
            $ruleValue = $ruleParts[1] ?? null;
            
            switch ($ruleName) {
                case 'required':
                    if (empty($value)) {
                        $errors[] = "{$field} 是必填项";
                    }
                    break;
                    
                case 'string':
                    if (!is_string($value)) {
                        $errors[] = "{$field} 必须是字符串";
                    }
                    break;
                    
                case 'integer':
                    if (!filter_var($value, FILTER_VALIDATE_INT)) {
                        $errors[] = "{$field} 必须是整数";
                    }
                    break;
                    
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "{$field} 必须是有效的邮箱地址";
                    }
                    break;
                    
                case 'url':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                        $errors[] = "{$field} 必须是有效的 URL";
                    }
                    break;
                    
                case 'min':
                    if (is_string($value) && strlen($value) < $ruleValue) {
                        $errors[] = "{$field} 最少需要 {$ruleValue} 个字符";
                    } elseif (is_numeric($value) && $value < $ruleValue) {
                        $errors[] = "{$field} 最小值为 {$ruleValue}";
                    }
                    break;
                    
                case 'max':
                    if (is_string($value) && strlen($value) > $ruleValue) {
                        $errors[] = "{$field} 最多允许 {$ruleValue} 个字符";
                    } elseif (is_numeric($value) && $value > $ruleValue) {
                        $errors[] = "{$field} 最大值为 {$ruleValue}";
                    }
                    break;
                    
                case 'regex':
                    if (!empty($value) && !preg_match($ruleValue, $value)) {
                        $errors[] = "{$field} 格式不正确";
                    }
                    break;
            }
        }
    }
    
    return $errors;
}

function showForm($request, $response) {
    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>参数验证测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 300px; padding: 8px; border: 1px solid #ddd; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .error { color: red; font-size: 12px; }
        .result { margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>参数验证测试表单</h1>
    
    <form id="validationForm" action="/validate" method="post">
        <div class="form-group">
            <label for="name">姓名 (必填, 2-50字符):</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="email">邮箱 (必填):</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="age">年龄 (必填, 1-120):</label>
            <input type="number" id="age" name="age" min="1" max="120" required>
        </div>
        
        <div class="form-group">
            <label for="website">网站 (可选):</label>
            <input type="url" id="website" name="website">
        </div>
        
        <div class="form-group">
            <label for="phone">手机号 (可选, 中国大陆格式):</label>
            <input type="tel" id="phone" name="phone" pattern="^1[3-9]\d{9}$">
        </div>
        
        <button type="submit">验证提交</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        document.getElementById("validationForm").onsubmit = function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch("/validate", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById("result");
                resultDiv.style.display = "block";
                resultDiv.innerHTML = "<h3>验证结果:</h3><pre>" + JSON.stringify(data, null, 2) + "</pre>";
            })
            .catch(error => {
                console.error("Error:", error);
            });
        };
    </script>
</body>
</html>';
    
    $response->header("Content-Type", "text/html; charset=utf-8");
    $response->end($html);
}

function showParameterInfo($request, $response) {
    $info = [
        'get_parameters' => $request->get ?? [],
        'post_parameters' => $request->post ?? [],
        'cookies' => $request->cookie ?? [],
        'raw_content' => $request->rawContent(),
        'content_type' => $request->header['content-type'] ?? 'unknown',
    ];
    
    // 尝试解析 JSON
    if (strpos($info['content_type'], 'application/json') !== false) {
        $info['json_data'] = json_decode($info['raw_content'], true);
        $info['json_error'] = json_last_error_msg();
    }
    
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->end(json_encode($info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

echo "参数处理服务器启动成功\n";
echo "访问 http://127.0.0.1:9501/form 查看表单\n";
echo "访问 http://127.0.0.1:9501 查看参数信息\n";
$server->start();
?>
```

## 6.2 文件上传处理

### 6.2.1 单文件和多文件上传

```php
<?php
// file_upload.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 配置上传限制
$server->set([
    'package_max_length' => 50 * 1024 * 1024, // 50MB
    'buffer_output_size' => 32 * 1024 * 1024, // 32MB
]);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];

    switch ($uri) {
        case '/':
            showUploadForm($response);
            break;
        case '/upload':
            handleFileUpload($request, $response);
            break;
        case '/list':
            listUploadedFiles($response);
            break;
        default:
            if (preg_match('/^\/download\/(.+)$/', $uri, $matches)) {
                handleFileDownload($matches[1], $response);
            } else {
                $response->status(404);
                $response->end("Not Found");
            }
            break;
    }
});

function showUploadForm($response) {
    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>文件上传测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; margin: 20px 0; text-align: center; }
        .upload-area.dragover { border-color: #007cba; background: #f0f8ff; }
        .file-list { margin-top: 20px; }
        .file-item { padding: 10px; border: 1px solid #ddd; margin: 5px 0; }
        .progress { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background: #007cba; transition: width 0.3s; }
    </style>
</head>
<body>
    <h1>Swoole 文件上传测试</h1>

    <h2>单文件上传</h2>
    <form id="singleForm" enctype="multipart/form-data">
        <input type="file" name="file" required>
        <button type="submit">上传</button>
    </form>

    <h2>多文件上传</h2>
    <form id="multiForm" enctype="multipart/form-data">
        <input type="file" name="files[]" multiple required>
        <button type="submit">批量上传</button>
    </form>

    <h2>拖拽上传</h2>
    <div id="dropArea" class="upload-area">
        <p>将文件拖拽到此处或点击选择文件</p>
        <input type="file" id="dragFile" multiple style="display: none;">
    </div>

    <div id="uploadResults" class="file-list"></div>

    <h2>已上传文件</h2>
    <div id="fileList"></div>
    <button onclick="loadFileList()">刷新文件列表</button>

    <script>
        // 单文件上传
        document.getElementById("singleForm").onsubmit = function(e) {
            e.preventDefault();
            uploadFiles(new FormData(this));
        };

        // 多文件上传
        document.getElementById("multiForm").onsubmit = function(e) {
            e.preventDefault();
            uploadFiles(new FormData(this));
        };

        // 拖拽上传
        const dropArea = document.getElementById("dropArea");
        const dragFile = document.getElementById("dragFile");

        dropArea.onclick = () => dragFile.click();

        dropArea.ondragover = (e) => {
            e.preventDefault();
            dropArea.classList.add("dragover");
        };

        dropArea.ondragleave = () => {
            dropArea.classList.remove("dragover");
        };

        dropArea.ondrop = (e) => {
            e.preventDefault();
            dropArea.classList.remove("dragover");

            const formData = new FormData();
            for (let file of e.dataTransfer.files) {
                formData.append("files[]", file);
            }
            uploadFiles(formData);
        };

        dragFile.onchange = function() {
            const formData = new FormData();
            for (let file of this.files) {
                formData.append("files[]", file);
            }
            uploadFiles(formData);
        };

        function uploadFiles(formData) {
            const xhr = new XMLHttpRequest();

            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percent = (e.loaded / e.total) * 100;
                    updateProgress(percent);
                }
            };

            xhr.onload = function() {
                if (xhr.status === 200) {
                    const result = JSON.parse(xhr.responseText);
                    showUploadResult(result);
                    loadFileList();
                } else {
                    alert("上传失败: " + xhr.statusText);
                }
            };

            xhr.open("POST", "/upload");
            xhr.send(formData);
        }

        function updateProgress(percent) {
            let progressDiv = document.getElementById("uploadProgress");
            if (!progressDiv) {
                progressDiv = document.createElement("div");
                progressDiv.id = "uploadProgress";
                progressDiv.innerHTML = \'<div class="progress"><div class="progress-bar" id="progressBar"></div></div>\';
                document.getElementById("uploadResults").appendChild(progressDiv);
            }

            document.getElementById("progressBar").style.width = percent + "%";

            if (percent >= 100) {
                setTimeout(() => {
                    progressDiv.remove();
                }, 1000);
            }
        }

        function showUploadResult(result) {
            const div = document.createElement("div");
            div.className = "file-item";
            div.innerHTML = "<h3>上传结果:</h3><pre>" + JSON.stringify(result, null, 2) + "</pre>";
            document.getElementById("uploadResults").appendChild(div);
        }

        function loadFileList() {
            fetch("/list")
                .then(response => response.json())
                .then(data => {
                    const listDiv = document.getElementById("fileList");
                    listDiv.innerHTML = "";

                    data.files.forEach(file => {
                        const fileDiv = document.createElement("div");
                        fileDiv.className = "file-item";
                        fileDiv.innerHTML = `
                            <strong>${file.name}</strong> (${file.size} bytes)
                            <br>上传时间: ${file.upload_time}
                            <br><a href="/download/${file.name}" target="_blank">下载</a>
                        `;
                        listDiv.appendChild(fileDiv);
                    });
                });
        }

        // 页面加载时获取文件列表
        loadFileList();
    </script>
</body>
</html>';

    $response->header("Content-Type", "text/html; charset=utf-8");
    $response->end($html);
}

function handleFileUpload($request, $response) {
    $uploadDir = __DIR__ . '/uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $results = [];
    $totalSize = 0;

    if (empty($request->files)) {
        $response->status(400);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => '没有上传文件']));
        return;
    }

    foreach ($request->files as $key => $fileGroup) {
        // 处理单文件和多文件的不同结构
        $files = [];
        if (isset($fileGroup['name']) && is_array($fileGroup['name'])) {
            // 多文件上传
            for ($i = 0; $i < count($fileGroup['name']); $i++) {
                $files[] = [
                    'name' => $fileGroup['name'][$i],
                    'type' => $fileGroup['type'][$i],
                    'tmp_name' => $fileGroup['tmp_name'][$i],
                    'error' => $fileGroup['error'][$i],
                    'size' => $fileGroup['size'][$i],
                ];
            }
        } else {
            // 单文件上传
            $files[] = $fileGroup;
        }

        foreach ($files as $file) {
            $result = processUploadedFile($file, $uploadDir);
            $results[] = $result;

            if ($result['status'] === 'success') {
                $totalSize += $result['size'];
            }
        }
    }

    $responseData = [
        'success' => true,
        'uploaded_files' => $results,
        'total_files' => count($results),
        'total_size' => $totalSize,
        'upload_time' => date('Y-m-d H:i:s'),
    ];

    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->end(json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function processUploadedFile($file, $uploadDir) {
    // 检查上传错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return [
            'original_name' => $file['name'],
            'status' => 'error',
            'error' => getUploadErrorMessage($file['error']),
        ];
    }

    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'text/plain', 'application/pdf'];
    if (!in_array($file['type'], $allowedTypes)) {
        return [
            'original_name' => $file['name'],
            'status' => 'error',
            'error' => '不支持的文件类型: ' . $file['type'],
        ];
    }

    // 验证文件大小 (10MB)
    $maxSize = 10 * 1024 * 1024;
    if ($file['size'] > $maxSize) {
        return [
            'original_name' => $file['name'],
            'status' => 'error',
            'error' => '文件过大，最大允许 10MB',
        ];
    }

    // 生成安全的文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $safeName = uniqid() . '_' . time() . '.' . $extension;
    $targetPath = $uploadDir . $safeName;

    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return [
            'original_name' => $file['name'],
            'saved_name' => $safeName,
            'size' => $file['size'],
            'type' => $file['type'],
            'path' => $targetPath,
            'status' => 'success',
        ];
    } else {
        return [
            'original_name' => $file['name'],
            'status' => 'error',
            'error' => '文件保存失败',
        ];
    }
}

function getUploadErrorMessage($errorCode) {
    $errors = [
        UPLOAD_ERR_INI_SIZE => '文件大小超过 php.ini 中 upload_max_filesize 的限制',
        UPLOAD_ERR_FORM_SIZE => '文件大小超过表单中 MAX_FILE_SIZE 的限制',
        UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
        UPLOAD_ERR_NO_FILE => '没有文件被上传',
        UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
        UPLOAD_ERR_CANT_WRITE => '文件写入失败',
        UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止',
    ];

    return $errors[$errorCode] ?? '未知上传错误';
}

function listUploadedFiles($response) {
    $uploadDir = __DIR__ . '/uploads/';
    $files = [];

    if (is_dir($uploadDir)) {
        $iterator = new DirectoryIterator($uploadDir);
        foreach ($iterator as $fileInfo) {
            if ($fileInfo->isFile()) {
                $files[] = [
                    'name' => $fileInfo->getFilename(),
                    'size' => $fileInfo->getSize(),
                    'upload_time' => date('Y-m-d H:i:s', $fileInfo->getMTime()),
                    'type' => mime_content_type($fileInfo->getPathname()),
                ];
            }
        }
    }

    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->end(json_encode(['files' => $files], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function handleFileDownload($filename, $response) {
    $uploadDir = __DIR__ . '/uploads/';
    $filePath = $uploadDir . basename($filename); // 防止路径遍历攻击

    if (!file_exists($filePath)) {
        $response->status(404);
        $response->end("文件不存在");
        return;
    }

    $fileSize = filesize($filePath);
    $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

    $response->header("Content-Type", $mimeType);
    $response->header("Content-Length", $fileSize);
    $response->header("Content-Disposition", "attachment; filename=\"" . basename($filename) . "\"");

    // 分块读取文件，避免大文件占用过多内存
    $handle = fopen($filePath, 'rb');
    if ($handle) {
        while (!feof($handle)) {
            $chunk = fread($handle, 8192); // 8KB 块
            $response->write($chunk);
        }
        fclose($handle);
    }

    $response->end();
}

echo "文件上传服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

## 6.3 响应类型处理

### 6.3.1 JSON 和 XML 响应

```php
<?php
// response_types.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];
    $accept = $request->header['accept'] ?? '*/*';

    // 示例数据
    $data = [
        'id' => 1,
        'name' => '张三',
        'email' => '<EMAIL>',
        'age' => 25,
        'skills' => ['PHP', 'JavaScript', 'Python'],
        'address' => [
            'city' => '北京',
            'district' => '朝阳区',
            'street' => '建国路88号'
        ],
        'created_at' => '2023-01-01 10:00:00',
        'updated_at' => date('Y-m-d H:i:s'),
    ];

    switch ($uri) {
        case '/json':
            sendJsonResponse($response, $data);
            break;
        case '/xml':
            sendXmlResponse($response, $data);
            break;
        case '/csv':
            sendCsvResponse($response, [$data]);
            break;
        case '/html':
            sendHtmlResponse($response, $data);
            break;
        case '/auto':
            sendAutoResponse($response, $data, $accept);
            break;
        default:
            sendMenuResponse($response);
            break;
    }
});

function sendJsonResponse($response, $data) {
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->header("X-Response-Type", "JSON");

    $jsonData = [
        'status' => 'success',
        'data' => $data,
        'meta' => [
            'timestamp' => time(),
            'format' => 'json',
            'version' => '1.0'
        ]
    ];

    $response->end(json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function sendXmlResponse($response, $data) {
    $response->header("Content-Type", "application/xml; charset=utf-8");
    $response->header("X-Response-Type", "XML");

    $xml = new SimpleXMLElement('<response/>');
    $xml->addChild('status', 'success');

    $dataNode = $xml->addChild('data');
    arrayToXml($data, $dataNode);

    $meta = $xml->addChild('meta');
    $meta->addChild('timestamp', time());
    $meta->addChild('format', 'xml');
    $meta->addChild('version', '1.0');

    $response->end($xml->asXML());
}

function arrayToXml($array, $xmlNode) {
    foreach ($array as $key => $value) {
        if (is_array($value)) {
            if (is_numeric($key)) {
                $key = 'item';
            }
            $subNode = $xmlNode->addChild($key);
            arrayToXml($value, $subNode);
        } else {
            $xmlNode->addChild($key, htmlspecialchars($value));
        }
    }
}

function sendCsvResponse($response, $dataArray) {
    $response->header("Content-Type", "text/csv; charset=utf-8");
    $response->header("Content-Disposition", "attachment; filename=\"data.csv\"");
    $response->header("X-Response-Type", "CSV");

    $output = fopen('php://temp', 'w');

    // 写入 BOM 以支持中文
    fwrite($output, "\xEF\xBB\xBF");

    // 写入表头
    if (!empty($dataArray)) {
        $headers = array_keys($dataArray[0]);
        fputcsv($output, $headers);

        // 写入数据
        foreach ($dataArray as $row) {
            $flatRow = [];
            foreach ($row as $value) {
                if (is_array($value)) {
                    $flatRow[] = json_encode($value, JSON_UNESCAPED_UNICODE);
                } else {
                    $flatRow[] = $value;
                }
            }
            fputcsv($output, $flatRow);
        }
    }

    rewind($output);
    $csvContent = stream_get_contents($output);
    fclose($output);

    $response->end($csvContent);
}

function sendHtmlResponse($response, $data) {
    $response->header("Content-Type", "text/html; charset=utf-8");
    $response->header("X-Response-Type", "HTML");

    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>用户信息</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .array-value { background: #f9f9f9; padding: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>用户信息</h1>
    <table>
        <tr><th>字段</th><th>值</th></tr>';

    foreach ($data as $key => $value) {
        $displayValue = $value;
        if (is_array($value)) {
            $displayValue = '<div class="array-value">' . json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</div>';
        }
        $html .= "<tr><td><strong>{$key}</strong></td><td>{$displayValue}</td></tr>";
    }

    $html .= '
    </table>

    <h2>其他格式</h2>
    <ul>
        <li><a href="/json">JSON 格式</a></li>
        <li><a href="/xml">XML 格式</a></li>
        <li><a href="/csv">CSV 格式</a></li>
        <li><a href="/auto">自动格式 (根据 Accept 头)</a></li>
    </ul>
</body>
</html>';

    $response->end($html);
}

function sendAutoResponse($response, $data, $accept) {
    // 根据 Accept 头自动选择格式
    if (strpos($accept, 'application/json') !== false) {
        sendJsonResponse($response, $data);
    } elseif (strpos($accept, 'application/xml') !== false || strpos($accept, 'text/xml') !== false) {
        sendXmlResponse($response, $data);
    } elseif (strpos($accept, 'text/csv') !== false) {
        sendCsvResponse($response, [$data]);
    } elseif (strpos($accept, 'text/html') !== false) {
        sendHtmlResponse($response, $data);
    } else {
        // 默认返回 JSON
        sendJsonResponse($response, $data);
    }
}

function sendMenuResponse($response) {
    $response->header("Content-Type", "text/html; charset=utf-8");

    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>响应类型演示</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .endpoint { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint a { text-decoration: none; color: #007cba; font-weight: bold; }
        .endpoint .desc { color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>Swoole 响应类型演示</h1>

    <div class="endpoint">
        <a href="/json">/json</a>
        <div class="desc">返回 JSON 格式数据</div>
    </div>

    <div class="endpoint">
        <a href="/xml">/xml</a>
        <div class="desc">返回 XML 格式数据</div>
    </div>

    <div class="endpoint">
        <a href="/csv">/csv</a>
        <div class="desc">返回 CSV 格式数据（下载）</div>
    </div>

    <div class="endpoint">
        <a href="/html">/html</a>
        <div class="desc">返回 HTML 格式数据</div>
    </div>

    <div class="endpoint">
        <a href="/auto">/auto</a>
        <div class="desc">根据 Accept 头自动选择格式</div>
    </div>

    <h2>测试命令</h2>
    <pre>
# JSON 格式
curl -H "Accept: application/json" http://127.0.0.1:9501/auto

# XML 格式
curl -H "Accept: application/xml" http://127.0.0.1:9501/auto

# HTML 格式
curl -H "Accept: text/html" http://127.0.0.1:9501/auto
    </pre>
</body>
</html>';

    $response->end($html);
}

echo "响应类型服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

### 6.3.2 流式响应和分块传输

```php
<?php
// streaming_response.php

use Swoole\Http\Server;
use Swoole\Coroutine;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];

    switch ($uri) {
        case '/stream':
            handleStreamResponse($response);
            break;
        case '/chunked':
            handleChunkedResponse($response);
            break;
        case '/sse':
            handleServerSentEvents($response);
            break;
        case '/download-large':
            handleLargeFileDownload($response);
            break;
        case '/progress':
            handleProgressResponse($response);
            break;
        default:
            showStreamingMenu($response);
            break;
    }
});

function handleStreamResponse($response) {
    $response->header("Content-Type", "text/plain; charset=utf-8");
    $response->header("Transfer-Encoding", "chunked");

    // 模拟实时数据流
    for ($i = 1; $i <= 10; $i++) {
        $data = "数据块 {$i}: " . date('Y-m-d H:i:s') . " - " . str_repeat('*', $i * 5) . "\n";
        $response->write($data);

        // 模拟处理延迟
        Coroutine::sleep(0.5);
    }

    $response->end("流式响应完成\n");
}

function handleChunkedResponse($response) {
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->header("Transfer-Encoding", "chunked");

    // 开始 JSON 数组
    $response->write('{"data": [');

    // 分块发送数据
    for ($i = 1; $i <= 5; $i++) {
        $item = [
            'id' => $i,
            'timestamp' => time(),
            'message' => "这是第 {$i} 条数据",
            'random' => rand(1000, 9999)
        ];

        if ($i > 1) {
            $response->write(',');
        }

        $response->write(json_encode($item, JSON_UNESCAPED_UNICODE));

        Coroutine::sleep(0.3);
    }

    // 结束 JSON 数组
    $response->end(']}');
}

function handleServerSentEvents($response) {
    $response->header("Content-Type", "text/event-stream");
    $response->header("Cache-Control", "no-cache");
    $response->header("Connection", "keep-alive");
    $response->header("Access-Control-Allow-Origin", "*");

    // 发送初始连接消息
    $response->write("data: " . json_encode(['type' => 'connected', 'message' => '连接已建立']) . "\n\n");

    // 持续发送事件
    for ($i = 1; $i <= 20; $i++) {
        $eventData = [
            'type' => 'update',
            'id' => $i,
            'timestamp' => time(),
            'message' => "实时更新 #{$i}",
            'progress' => ($i / 20) * 100
        ];

        $response->write("id: {$i}\n");
        $response->write("event: update\n");
        $response->write("data: " . json_encode($eventData, JSON_UNESCAPED_UNICODE) . "\n\n");

        Coroutine::sleep(1);
    }

    // 发送完成事件
    $response->write("event: complete\n");
    $response->write("data: " . json_encode(['type' => 'complete', 'message' => '数据传输完成']) . "\n\n");

    $response->end();
}

function handleLargeFileDownload($response) {
    // 模拟大文件下载
    $fileSize = 10 * 1024 * 1024; // 10MB
    $chunkSize = 64 * 1024; // 64KB 块

    $response->header("Content-Type", "application/octet-stream");
    $response->header("Content-Length", $fileSize);
    $response->header("Content-Disposition", "attachment; filename=\"large_file.dat\"");

    $sent = 0;
    while ($sent < $fileSize) {
        $remaining = $fileSize - $sent;
        $currentChunkSize = min($chunkSize, $remaining);

        // 生成随机数据块
        $chunk = str_repeat(chr(rand(65, 90)), $currentChunkSize);
        $response->write($chunk);

        $sent += $currentChunkSize;

        // 模拟网络延迟
        Coroutine::sleep(0.01);
    }

    $response->end();
}

function handleProgressResponse($response) {
    $response->header("Content-Type", "text/html; charset=utf-8");

    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>实时进度演示</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .progress { width: 100%; height: 30px; background: #f0f0f0; border-radius: 15px; overflow: hidden; margin: 10px 0; }
        .progress-bar { height: 100%; background: linear-gradient(90deg, #007cba, #00a8e6); transition: width 0.3s; }
        .log { height: 300px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Server-Sent Events 演示</h1>

    <div>
        <button onclick="startSSE()">开始接收实时数据</button>
        <button onclick="stopSSE()">停止接收</button>
    </div>

    <div class="progress">
        <div id="progressBar" class="progress-bar" style="width: 0%"></div>
    </div>

    <div id="status">等待连接...</div>

    <div id="log" class="log"></div>

    <script>
        let eventSource = null;

        function startSSE() {
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource("/sse");

            eventSource.onopen = function() {
                document.getElementById("status").textContent = "已连接到服务器";
                addLog("连接已建立");
            };

            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addLog("收到消息: " + JSON.stringify(data));
            };

            eventSource.addEventListener("update", function(event) {
                const data = JSON.parse(event.data);
                document.getElementById("progressBar").style.width = data.progress + "%";
                document.getElementById("status").textContent = data.message + " (" + data.progress.toFixed(1) + "%)";
                addLog("进度更新: " + data.message);
            });

            eventSource.addEventListener("complete", function(event) {
                const data = JSON.parse(event.data);
                document.getElementById("status").textContent = data.message;
                addLog("传输完成");
                eventSource.close();
            });

            eventSource.onerror = function() {
                document.getElementById("status").textContent = "连接错误";
                addLog("连接发生错误");
            };
        }

        function stopSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                document.getElementById("status").textContent = "连接已断开";
                addLog("手动断开连接");
            }
        }

        function addLog(message) {
            const log = document.getElementById("log");
            const time = new Date().toLocaleTimeString();
            log.innerHTML += "[" + time + "] " + message + "\\n";
            log.scrollTop = log.scrollHeight;
        }
    </script>
</body>
</html>';

    $response->end($html);
}

function showStreamingMenu($response) {
    $response->header("Content-Type", "text/html; charset=utf-8");

    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>流式响应演示</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .endpoint { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint a { text-decoration: none; color: #007cba; font-weight: bold; }
        .endpoint .desc { color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>Swoole 流式响应演示</h1>

    <div class="endpoint">
        <a href="/stream">/stream</a>
        <div class="desc">基本流式响应，分块发送文本数据</div>
    </div>

    <div class="endpoint">
        <a href="/chunked">/chunked</a>
        <div class="desc">分块传输编码，发送 JSON 数据</div>
    </div>

    <div class="endpoint">
        <a href="/sse">/sse</a>
        <div class="desc">Server-Sent Events，实时事件流</div>
    </div>

    <div class="endpoint">
        <a href="/download-large">/download-large</a>
        <div class="desc">大文件下载，分块传输</div>
    </div>

    <div class="endpoint">
        <a href="/progress">/progress</a>
        <div class="desc">实时进度演示页面</div>
    </div>
</body>
</html>';

    $response->end($html);
}

echo "流式响应服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

## 本章练习

### 练习 1：RESTful API 服务器
创建一个完整的 RESTful API 服务器，支持用户管理：

```php
<?php
// restful_api.php

use Swoole\Http\Server;

// 模拟数据存储
$users = [
    1 => ['id' => 1, 'name' => '张三', 'email' => '<EMAIL>', 'age' => 25],
    2 => ['id' => 2, 'name' => '李四', 'email' => '<EMAIL>', 'age' => 30],
];

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use (&$users) {
    // 实现功能：
    // 1. GET /users - 获取所有用户
    // 2. GET /users/{id} - 获取指定用户
    // 3. POST /users - 创建新用户
    // 4. PUT /users/{id} - 更新用户
    // 5. DELETE /users/{id} - 删除用户
    // 6. 参数验证和错误处理
    // 7. 支持分页和搜索
});

$server->start();
?>
```

### 练习 2：图片处理服务器
实现一个图片上传和处理服务器：

```php
<?php
// image_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现功能：
    // 1. 图片上传和验证
    // 2. 图片缩放和裁剪
    // 3. 图片格式转换
    // 4. 图片水印添加
    // 5. 缩略图生成
    // 6. 图片信息获取
});

$server->start();
?>
```

### 练习 3：实时日志查看器
创建一个实时日志查看器，使用 Server-Sent Events：

```php
<?php
// log_viewer.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现功能：
    // 1. 实时监控日志文件变化
    // 2. 通过 SSE 推送新日志
    // 3. 日志过滤和搜索
    // 4. 多文件监控
    // 5. 日志级别分类显示
});

$server->start();
?>
```

### 练习 4：文件分片上传
实现大文件分片上传功能：

```php
<?php
// chunk_upload.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现功能：
    // 1. 文件分片上传
    // 2. 分片合并
    // 3. 上传进度跟踪
    // 4. 断点续传
    // 5. 文件完整性校验
    // 6. 并发上传控制
});

$server->start();
?>
```

## 本章小结

本章深入介绍了 Swoole HTTP 服务器的请求和响应处理，包括请求解析、参数验证、文件上传、各种响应类型和流式处理。这些技能是构建现代 Web 应用的基础。

**关键要点：**

- **请求解析**：理解 HTTP 请求的各个组成部分
- **参数处理**：掌握参数验证和安全处理方法
- **文件上传**：实现安全可靠的文件上传功能
- **响应类型**：支持多种数据格式的响应
- **流式处理**：处理大数据量和实时数据传输
- **性能优化**：合理使用缓冲区和分块传输

**最佳实践：**

1. 始终验证和清理用户输入
2. 合理设置文件上传限制
3. 使用适当的响应格式
4. 对大文件使用流式处理
5. 实现适当的错误处理机制

**下一章预告：**
下一章我们将学习如何构建路由系统，实现更复杂的 URL 处理和请求分发机制。
```
