# 第10章：WebSocket 基础概念

## 学习目标
- 理解 WebSocket 协议的原理和特点
- 掌握 WebSocket 与 HTTP 的区别
- 学会 WebSocket 的握手过程
- 了解 WebSocket 的应用场景

## 10.1 WebSocket 协议简介

### 10.1.1 什么是 WebSocket

WebSocket 是一种在单个 TCP 连接上进行全双工通信的协议。它使得客户端和服务器之间的数据交换变得更加简单，允许服务端主动向客户端推送数据。

**WebSocket 的特点：**
- **全双工通信**：客户端和服务器可以同时发送数据
- **实时性**：低延迟的实时数据传输
- **持久连接**：连接建立后保持开放状态
- **较少的开销**：相比 HTTP 轮询，减少了网络开销

```php
<?php
// websocket_demo.php

use Swoole\WebSocket\Server;
use Swoole\Http\Request;
use Swoole\WebSocket\Frame;

$server = new Server("0.0.0.0", 9501);

// WebSocket 连接建立时触发
$server->on('open', function (Server $server, Request $request) {
    echo "新的 WebSocket 连接: {$request->fd}\n";
    echo "客户端 IP: {$request->server['remote_addr']}\n";
    echo "握手时间: " . date('Y-m-d H:i:s') . "\n";
    
    // 向客户端发送欢迎消息
    $server->push($request->fd, json_encode([
        'type' => 'welcome',
        'message' => '欢迎连接到 WebSocket 服务器',
        'fd' => $request->fd,
        'time' => date('Y-m-d H:i:s')
    ]));
});

// 接收到 WebSocket 消息时触发
$server->on('message', function (Server $server, Frame $frame) {
    echo "收到来自 {$frame->fd} 的消息: {$frame->data}\n";
    
    // 解析消息
    $data = json_decode($frame->data, true);
    
    if ($data) {
        switch ($data['type']) {
            case 'ping':
                // 心跳响应
                $server->push($frame->fd, json_encode([
                    'type' => 'pong',
                    'time' => date('Y-m-d H:i:s')
                ]));
                break;
                
            case 'echo':
                // 回显消息
                $server->push($frame->fd, json_encode([
                    'type' => 'echo',
                    'original' => $data['message'],
                    'response' => '服务器收到: ' . $data['message'],
                    'time' => date('Y-m-d H:i:s')
                ]));
                break;
                
            case 'broadcast':
                // 广播消息给所有连接
                $broadcastData = json_encode([
                    'type' => 'broadcast',
                    'from' => $frame->fd,
                    'message' => $data['message'],
                    'time' => date('Y-m-d H:i:s')
                ]);
                
                foreach ($server->connections as $fd) {
                    if ($server->isEstablished($fd)) {
                        $server->push($fd, $broadcastData);
                    }
                }
                break;
                
            default:
                $server->push($frame->fd, json_encode([
                    'type' => 'error',
                    'message' => '未知的消息类型',
                    'time' => date('Y-m-d H:i:s')
                ]));
                break;
        }
    } else {
        // 处理纯文本消息
        $server->push($frame->fd, "服务器收到: " . $frame->data);
    }
});

// WebSocket 连接关闭时触发
$server->on('close', function (Server $server, int $fd) {
    echo "WebSocket 连接关闭: {$fd}\n";
});

// 处理 HTTP 请求（提供测试页面）
$server->on('request', function ($request, $response) {
    if ($request->server['request_uri'] === '/') {
        $html = '
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket 测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .messages { height: 300px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; margin-bottom: 10px; }
        .input-group { margin-bottom: 10px; }
        input, button, select { padding: 8px; margin: 2px; }
        input[type="text"] { width: 300px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 测试客户端</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="input-group">
            <button id="connectBtn">连接</button>
            <button id="disconnectBtn" disabled>断开连接</button>
        </div>
        
        <div id="messages" class="messages"></div>
        
        <div class="input-group">
            <select id="messageType">
                <option value="echo">回显消息</option>
                <option value="broadcast">广播消息</option>
                <option value="ping">心跳测试</option>
            </select>
            <input type="text" id="messageInput" placeholder="输入消息" disabled>
            <button id="sendBtn" disabled>发送</button>
        </div>
        
        <div class="input-group">
            <button id="clearBtn">清空消息</button>
        </div>
    </div>
    
    <script>
        let ws = null;
        const status = document.getElementById("status");
        const messages = document.getElementById("messages");
        const connectBtn = document.getElementById("connectBtn");
        const disconnectBtn = document.getElementById("disconnectBtn");
        const messageInput = document.getElementById("messageInput");
        const sendBtn = document.getElementById("sendBtn");
        const messageType = document.getElementById("messageType");
        const clearBtn = document.getElementById("clearBtn");
        
        function addMessage(message, type = "info") {
            const div = document.createElement("div");
            div.style.color = type === "error" ? "red" : type === "sent" ? "blue" : "black";
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function updateStatus(connected) {
            if (connected) {
                status.textContent = "已连接";
                status.className = "status connected";
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                messageInput.disabled = false;
                sendBtn.disabled = false;
            } else {
                status.textContent = "未连接";
                status.className = "status disconnected";
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                messageInput.disabled = true;
                sendBtn.disabled = true;
            }
        }
        
        connectBtn.onclick = function() {
            ws = new WebSocket("ws://127.0.0.1:9501");
            
            ws.onopen = function() {
                addMessage("WebSocket 连接已建立");
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage(`收到: ${JSON.stringify(data, null, 2)}`);
                } catch (e) {
                    addMessage(`收到: ${event.data}`);
                }
            };
            
            ws.onclose = function() {
                addMessage("WebSocket 连接已关闭");
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                addMessage("WebSocket 错误: " + error, "error");
            };
        };
        
        disconnectBtn.onclick = function() {
            if (ws) {
                ws.close();
            }
        };
        
        sendBtn.onclick = function() {
            const message = messageInput.value.trim();
            const type = messageType.value;
            
            if (message && ws && ws.readyState === WebSocket.OPEN) {
                const data = {
                    type: type,
                    message: message
                };
                
                ws.send(JSON.stringify(data));
                addMessage(`发送: ${JSON.stringify(data)}`, "sent");
                messageInput.value = "";
            }
        };
        
        messageInput.onkeypress = function(e) {
            if (e.key === "Enter") {
                sendBtn.click();
            }
        };
        
        clearBtn.onclick = function() {
            messages.innerHTML = "";
        };
        
        // 页面加载时自动连接
        window.onload = function() {
            connectBtn.click();
        };
    </script>
</body>
</html>';
        
        $response->header("Content-Type", "text/html; charset=utf-8");
        $response->end($html);
    } else {
        $response->status(404);
        $response->end("Not Found");
    }
});

echo "WebSocket 服务器启动成功\n";
echo "WebSocket 地址: ws://127.0.0.1:9501\n";
echo "测试页面: http://127.0.0.1:9501\n";
$server->start();
?>
```

### 10.1.2 WebSocket vs HTTP

| 特性 | HTTP | WebSocket |
|------|------|-----------|
| 连接类型 | 无状态，短连接 | 有状态，长连接 |
| 通信方向 | 单向（客户端请求） | 双向（全双工） |
| 协议开销 | 每次请求都有 HTTP 头 | 握手后开销很小 |
| 实时性 | 需要轮询 | 真正的实时通信 |
| 服务器推送 | 不支持 | 原生支持 |
| 浏览器支持 | 所有浏览器 | 现代浏览器 |

### 10.1.3 WebSocket 握手过程

```php
<?php
// websocket_handshake.php

class WebSocketHandshake {
    public static function demonstrate() {
        echo "=== WebSocket 握手过程演示 ===\n\n";
        
        // 1. 客户端发送握手请求
        echo "1. 客户端发送 HTTP 升级请求:\n";
        echo "GET /chat HTTP/1.1\n";
        echo "Host: example.com:9501\n";
        echo "Upgrade: websocket\n";
        echo "Connection: Upgrade\n";
        echo "Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\n";
        echo "Sec-WebSocket-Version: 13\n\n";
        
        // 2. 服务器验证并响应
        echo "2. 服务器验证 Sec-WebSocket-Key 并响应:\n";
        $key = "dGhlIHNhbXBsZSBub25jZQ==";
        $acceptKey = base64_encode(sha1($key . "258EAFA5-E914-47DA-95CA-C5AB0DC85B11", true));
        
        echo "HTTP/1.1 101 Switching Protocols\n";
        echo "Upgrade: websocket\n";
        echo "Connection: Upgrade\n";
        echo "Sec-WebSocket-Accept: {$acceptKey}\n\n";
        
        // 3. 握手完成
        echo "3. 握手完成，WebSocket 连接建立\n";
        echo "   - 协议从 HTTP 升级为 WebSocket\n";
        echo "   - 连接保持开放状态\n";
        echo "   - 可以进行双向数据传输\n\n";
        
        // 4. 数据帧格式
        echo "4. WebSocket 数据帧格式:\n";
        echo "   0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1\n";
        echo "  +-+-+-+-+-------+-+-------------+-------------------------------+\n";
        echo "  |F|R|R|R| opcode|M| Payload len |    Extended payload length    |\n";
        echo "  |I|S|S|S|  (4)  |A|     (7)     |             (16/64)           |\n";
        echo "  |N|V|V|V|       |S|             |   (if payload len==126/127)   |\n";
        echo "  | |1|2|3|       |K|             |                               |\n";
        echo "  +-+-+-+-+-------+-+-------------+ - - - - - - - - - - - - - - - +\n";
        echo "  |     Extended payload length continued, if payload len == 127  |\n";
        echo "  + - - - - - - - - - - - - - - - +-------------------------------+\n";
        echo "  |                               |Masking-key, if MASK set to 1  |\n";
        echo "  +-------------------------------+-------------------------------+\n";
        echo "  | Masking-key (continued)       |          Payload Data         |\n";
        echo "  +-------------------------------- - - - - - - - - - - - - - - - +\n";
        echo "  :                     Payload Data continued ...                :\n";
        echo "  + - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - +\n";
        echo "  |                     Payload Data continued ...                |\n";
        echo "  +---------------------------------------------------------------+\n";
    }
}

WebSocketHandshake::demonstrate();
?>
```

## 10.2 WebSocket 应用场景

### 10.2.1 实时通信应用

WebSocket 特别适合需要实时双向通信的应用场景：

**1. 即时聊天系统**
```php
<?php
// chat_example.php

use Swoole\WebSocket\Server;

class ChatServer {
    private $server;
    private $users = [];
    private $rooms = [];

    public function __construct() {
        $this->server = new Server("0.0.0.0", 9501);
        $this->setupEvents();
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
    }

    public function onOpen($server, $request) {
        echo "用户 {$request->fd} 连接聊天室\n";

        // 发送欢迎消息
        $this->sendToUser($request->fd, [
            'type' => 'system',
            'message' => '欢迎来到聊天室！请输入用户名。'
        ]);
    }

    public function onMessage($server, $frame) {
        $data = json_decode($frame->data, true);

        switch ($data['type']) {
            case 'join':
                $this->handleJoin($frame->fd, $data);
                break;
            case 'message':
                $this->handleMessage($frame->fd, $data);
                break;
            case 'private':
                $this->handlePrivateMessage($frame->fd, $data);
                break;
        }
    }

    public function onClose($server, $fd) {
        if (isset($this->users[$fd])) {
            $username = $this->users[$fd]['username'];
            $room = $this->users[$fd]['room'];

            unset($this->users[$fd]);

            // 通知其他用户
            $this->broadcastToRoom($room, [
                'type' => 'user_left',
                'username' => $username,
                'message' => "{$username} 离开了聊天室"
            ], $fd);

            echo "用户 {$username} ({$fd}) 离开聊天室\n";
        }
    }

    private function handleJoin($fd, $data) {
        $username = $data['username'];
        $room = $data['room'] ?? 'general';

        $this->users[$fd] = [
            'username' => $username,
            'room' => $room,
            'join_time' => time()
        ];

        if (!isset($this->rooms[$room])) {
            $this->rooms[$room] = [];
        }
        $this->rooms[$room][] = $fd;

        // 通知用户加入成功
        $this->sendToUser($fd, [
            'type' => 'joined',
            'room' => $room,
            'username' => $username
        ]);

        // 通知其他用户
        $this->broadcastToRoom($room, [
            'type' => 'user_joined',
            'username' => $username,
            'message' => "{$username} 加入了聊天室"
        ], $fd);

        echo "用户 {$username} 加入房间 {$room}\n";
    }

    private function handleMessage($fd, $data) {
        if (!isset($this->users[$fd])) {
            return;
        }

        $user = $this->users[$fd];
        $message = [
            'type' => 'message',
            'username' => $user['username'],
            'message' => $data['message'],
            'time' => date('H:i:s')
        ];

        $this->broadcastToRoom($user['room'], $message);
    }

    private function handlePrivateMessage($fd, $data) {
        $targetUsername = $data['target'];
        $targetFd = $this->findUserByUsername($targetUsername);

        if ($targetFd) {
            $sender = $this->users[$fd];
            $message = [
                'type' => 'private',
                'from' => $sender['username'],
                'message' => $data['message'],
                'time' => date('H:i:s')
            ];

            $this->sendToUser($targetFd, $message);
            $this->sendToUser($fd, [
                'type' => 'private_sent',
                'to' => $targetUsername,
                'message' => $data['message']
            ]);
        } else {
            $this->sendToUser($fd, [
                'type' => 'error',
                'message' => "用户 {$targetUsername} 不在线"
            ]);
        }
    }

    private function sendToUser($fd, $data) {
        if ($this->server->isEstablished($fd)) {
            $this->server->push($fd, json_encode($data));
        }
    }

    private function broadcastToRoom($room, $data, $excludeFd = null) {
        if (!isset($this->rooms[$room])) {
            return;
        }

        foreach ($this->rooms[$room] as $fd) {
            if ($fd !== $excludeFd && $this->server->isEstablished($fd)) {
                $this->server->push($fd, json_encode($data));
            }
        }
    }

    private function findUserByUsername($username) {
        foreach ($this->users as $fd => $user) {
            if ($user['username'] === $username) {
                return $fd;
            }
        }
        return null;
    }

    public function start() {
        echo "聊天服务器启动在 ws://0.0.0.0:9501\n";
        $this->server->start();
    }
}

$chatServer = new ChatServer();
$chatServer->start();
?>
```

**2. 实时数据推送**
```php
<?php
// realtime_data.php

use Swoole\WebSocket\Server;
use Swoole\Timer;

class RealTimeDataServer {
    private $server;
    private $subscribers = [];

    public function __construct() {
        $this->server = new Server("0.0.0.0", 9502);
        $this->setupEvents();
        $this->startDataGeneration();
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
    }

    public function onOpen($server, $request) {
        echo "新的数据订阅者: {$request->fd}\n";

        // 发送当前数据
        $this->sendCurrentData($request->fd);
    }

    public function onMessage($server, $frame) {
        $data = json_decode($frame->data, true);

        switch ($data['type']) {
            case 'subscribe':
                $this->handleSubscribe($frame->fd, $data);
                break;
            case 'unsubscribe':
                $this->handleUnsubscribe($frame->fd, $data);
                break;
        }
    }

    public function onClose($server, $fd) {
        unset($this->subscribers[$fd]);
        echo "订阅者 {$fd} 断开连接\n";
    }

    private function handleSubscribe($fd, $data) {
        $channels = $data['channels'] ?? [];
        $this->subscribers[$fd] = $channels;

        $this->server->push($fd, json_encode([
            'type' => 'subscribed',
            'channels' => $channels
        ]));
    }

    private function handleUnsubscribe($fd, $data) {
        $channels = $data['channels'] ?? [];

        if (isset($this->subscribers[$fd])) {
            $this->subscribers[$fd] = array_diff($this->subscribers[$fd], $channels);
        }

        $this->server->push($fd, json_encode([
            'type' => 'unsubscribed',
            'channels' => $channels
        ]));
    }

    private function sendCurrentData($fd) {
        $data = [
            'type' => 'initial_data',
            'stock_prices' => $this->generateStockPrices(),
            'system_stats' => $this->generateSystemStats(),
            'user_activity' => $this->generateUserActivity()
        ];

        $this->server->push($fd, json_encode($data));
    }

    private function startDataGeneration() {
        // 每秒推送股票价格
        Timer::tick(1000, function() {
            $this->broadcastToChannel('stock_prices', [
                'type' => 'stock_update',
                'data' => $this->generateStockPrices(),
                'timestamp' => time()
            ]);
        });

        // 每5秒推送系统统计
        Timer::tick(5000, function() {
            $this->broadcastToChannel('system_stats', [
                'type' => 'system_update',
                'data' => $this->generateSystemStats(),
                'timestamp' => time()
            ]);
        });

        // 每3秒推送用户活动
        Timer::tick(3000, function() {
            $this->broadcastToChannel('user_activity', [
                'type' => 'activity_update',
                'data' => $this->generateUserActivity(),
                'timestamp' => time()
            ]);
        });
    }

    private function broadcastToChannel($channel, $data) {
        foreach ($this->subscribers as $fd => $channels) {
            if (in_array($channel, $channels) && $this->server->isEstablished($fd)) {
                $this->server->push($fd, json_encode($data));
            }
        }
    }

    private function generateStockPrices() {
        $stocks = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'];
        $prices = [];

        foreach ($stocks as $stock) {
            $prices[$stock] = [
                'price' => round(rand(100, 300) + rand(0, 99) / 100, 2),
                'change' => round((rand(-500, 500) / 100), 2),
                'volume' => rand(1000000, 10000000)
            ];
        }

        return $prices;
    }

    private function generateSystemStats() {
        return [
            'cpu_usage' => rand(10, 90),
            'memory_usage' => rand(30, 80),
            'disk_usage' => rand(20, 70),
            'network_in' => rand(1000, 50000),
            'network_out' => rand(1000, 50000),
            'active_connections' => count($this->subscribers)
        ];
    }

    private function generateUserActivity() {
        return [
            'online_users' => rand(100, 1000),
            'new_registrations' => rand(0, 20),
            'active_sessions' => rand(50, 500),
            'page_views' => rand(1000, 10000)
        ];
    }

    public function start() {
        echo "实时数据服务器启动在 ws://0.0.0.0:9502\n";
        $this->server->start();
    }
}

$dataServer = new RealTimeDataServer();
$dataServer->start();
?>
```

### 10.2.2 游戏和协作应用

**3. 多人在线游戏**
```php
<?php
// game_server.php

use Swoole\WebSocket\Server;
use Swoole\Timer;

class GameServer {
    private $server;
    private $players = [];
    private $gameRooms = [];
    private $gameLoop;

    public function __construct() {
        $this->server = new Server("0.0.0.0", 9503);
        $this->setupEvents();
        $this->startGameLoop();
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
    }

    public function onOpen($server, $request) {
        echo "玩家 {$request->fd} 连接游戏服务器\n";
    }

    public function onMessage($server, $frame) {
        $data = json_decode($frame->data, true);

        switch ($data['type']) {
            case 'join_game':
                $this->handleJoinGame($frame->fd, $data);
                break;
            case 'player_move':
                $this->handlePlayerMove($frame->fd, $data);
                break;
            case 'player_action':
                $this->handlePlayerAction($frame->fd, $data);
                break;
        }
    }

    public function onClose($server, $fd) {
        if (isset($this->players[$fd])) {
            $player = $this->players[$fd];
            $roomId = $player['room_id'];

            // 从房间移除玩家
            if (isset($this->gameRooms[$roomId])) {
                $this->gameRooms[$roomId]['players'] = array_filter(
                    $this->gameRooms[$roomId]['players'],
                    function($p) use ($fd) { return $p['fd'] !== $fd; }
                );

                // 通知其他玩家
                $this->broadcastToRoom($roomId, [
                    'type' => 'player_left',
                    'player_id' => $fd
                ], $fd);
            }

            unset($this->players[$fd]);
            echo "玩家 {$fd} 离开游戏\n";
        }
    }

    private function handleJoinGame($fd, $data) {
        $playerName = $data['player_name'];
        $roomId = $data['room_id'] ?? 'room_1';

        // 创建玩家
        $this->players[$fd] = [
            'fd' => $fd,
            'name' => $playerName,
            'room_id' => $roomId,
            'x' => rand(0, 800),
            'y' => rand(0, 600),
            'score' => 0,
            'health' => 100
        ];

        // 创建或加入房间
        if (!isset($this->gameRooms[$roomId])) {
            $this->gameRooms[$roomId] = [
                'id' => $roomId,
                'players' => [],
                'created_at' => time()
            ];
        }

        $this->gameRooms[$roomId]['players'][] = $this->players[$fd];

        // 发送游戏状态给新玩家
        $this->server->push($fd, json_encode([
            'type' => 'game_joined',
            'room_id' => $roomId,
            'player_id' => $fd,
            'game_state' => $this->getGameState($roomId)
        ]));

        // 通知其他玩家
        $this->broadcastToRoom($roomId, [
            'type' => 'player_joined',
            'player' => $this->players[$fd]
        ], $fd);

        echo "玩家 {$playerName} 加入房间 {$roomId}\n";
    }

    private function handlePlayerMove($fd, $data) {
        if (!isset($this->players[$fd])) {
            return;
        }

        $player = &$this->players[$fd];
        $player['x'] = $data['x'];
        $player['y'] = $data['y'];

        // 更新房间中的玩家信息
        $roomId = $player['room_id'];
        if (isset($this->gameRooms[$roomId])) {
            foreach ($this->gameRooms[$roomId]['players'] as &$roomPlayer) {
                if ($roomPlayer['fd'] === $fd) {
                    $roomPlayer = $player;
                    break;
                }
            }
        }

        // 广播玩家移动
        $this->broadcastToRoom($roomId, [
            'type' => 'player_moved',
            'player_id' => $fd,
            'x' => $player['x'],
            'y' => $player['y']
        ], $fd);
    }

    private function handlePlayerAction($fd, $data) {
        if (!isset($this->players[$fd])) {
            return;
        }

        $player = &$this->players[$fd];
        $roomId = $player['room_id'];

        switch ($data['action']) {
            case 'attack':
                $this->processAttack($fd, $data);
                break;
            case 'collect_item':
                $this->processItemCollection($fd, $data);
                break;
        }
    }

    private function processAttack($attackerId, $data) {
        $attacker = $this->players[$attackerId];
        $roomId = $attacker['room_id'];

        // 简单的攻击逻辑
        $damage = rand(10, 30);

        $this->broadcastToRoom($roomId, [
            'type' => 'player_attack',
            'attacker_id' => $attackerId,
            'damage' => $damage,
            'x' => $data['x'],
            'y' => $data['y']
        ]);
    }

    private function processItemCollection($playerId, $data) {
        $player = &$this->players[$playerId];
        $player['score'] += 10;

        $this->server->push($playerId, json_encode([
            'type' => 'item_collected',
            'score' => $player['score']
        ]));
    }

    private function startGameLoop() {
        // 游戏主循环，每100ms执行一次
        $this->gameLoop = Timer::tick(100, function() {
            foreach ($this->gameRooms as $roomId => $room) {
                $this->updateGameRoom($roomId);
            }
        });
    }

    private function updateGameRoom($roomId) {
        if (!isset($this->gameRooms[$roomId]) || empty($this->gameRooms[$roomId]['players'])) {
            return;
        }

        // 生成随机物品
        if (rand(1, 100) <= 5) { // 5% 概率
            $item = [
                'type' => 'item_spawned',
                'item_id' => uniqid(),
                'x' => rand(0, 800),
                'y' => rand(0, 600),
                'item_type' => 'coin'
            ];

            $this->broadcastToRoom($roomId, $item);
        }

        // 发送游戏状态更新
        $gameState = [
            'type' => 'game_update',
            'players' => $this->gameRooms[$roomId]['players'],
            'timestamp' => microtime(true)
        ];

        $this->broadcastToRoom($roomId, $gameState);
    }

    private function getGameState($roomId) {
        return [
            'room_id' => $roomId,
            'players' => $this->gameRooms[$roomId]['players'] ?? [],
            'timestamp' => time()
        ];
    }

    private function broadcastToRoom($roomId, $data, $excludeFd = null) {
        if (!isset($this->gameRooms[$roomId])) {
            return;
        }

        foreach ($this->gameRooms[$roomId]['players'] as $player) {
            $fd = $player['fd'];
            if ($fd !== $excludeFd && $this->server->isEstablished($fd)) {
                $this->server->push($fd, json_encode($data));
            }
        }
    }

    public function start() {
        echo "游戏服务器启动在 ws://0.0.0.0:9503\n";
        $this->server->start();
    }
}

$gameServer = new GameServer();
$gameServer->start();
?>
```

## 10.3 WebSocket 安全考虑

### 10.3.1 常见安全问题

1. **跨站 WebSocket 劫持 (CSWSH)**
2. **消息注入攻击**
3. **拒绝服务攻击 (DoS)**
4. **数据泄露**

```php
<?php
// websocket_security.php

use Swoole\WebSocket\Server;

class SecureWebSocketServer {
    private $server;
    private $allowedOrigins = ['http://localhost', 'https://example.com'];
    private $connectionLimits = [];
    private $rateLimits = [];

    public function __construct() {
        $this->server = new Server("0.0.0.0", 9501);
        $this->setupEvents();
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
    }

    public function onOpen($server, $request) {
        // 1. 验证来源
        if (!$this->validateOrigin($request)) {
            $server->close($request->fd);
            return;
        }

        // 2. 检查连接限制
        if (!$this->checkConnectionLimit($request)) {
            $server->close($request->fd);
            return;
        }

        // 3. 验证认证信息
        if (!$this->validateAuth($request)) {
            $server->close($request->fd);
            return;
        }

        echo "安全连接建立: {$request->fd}\n";
    }

    public function onMessage($server, $frame) {
        // 1. 检查消息频率限制
        if (!$this->checkRateLimit($frame->fd)) {
            $server->push($frame->fd, json_encode([
                'error' => 'Rate limit exceeded'
            ]));
            return;
        }

        // 2. 验证消息格式
        $data = $this->validateMessage($frame->data);
        if (!$data) {
            $server->push($frame->fd, json_encode([
                'error' => 'Invalid message format'
            ]));
            return;
        }

        // 3. 过滤危险内容
        $data = $this->sanitizeMessage($data);

        // 处理消息
        $this->handleSecureMessage($server, $frame->fd, $data);
    }

    public function onClose($server, $fd) {
        unset($this->connectionLimits[$fd]);
        unset($this->rateLimits[$fd]);
        echo "连接关闭: {$fd}\n";
    }

    private function validateOrigin($request) {
        $origin = $request->header['origin'] ?? '';

        if (empty($origin)) {
            echo "拒绝连接: 缺少 Origin 头部\n";
            return false;
        }

        if (!in_array($origin, $this->allowedOrigins)) {
            echo "拒绝连接: 不允许的来源 {$origin}\n";
            return false;
        }

        return true;
    }

    private function checkConnectionLimit($request) {
        $ip = $request->server['remote_addr'];
        $maxConnections = 10; // 每个IP最多10个连接

        $currentConnections = 0;
        foreach ($this->connectionLimits as $connIp) {
            if ($connIp === $ip) {
                $currentConnections++;
            }
        }

        if ($currentConnections >= $maxConnections) {
            echo "拒绝连接: IP {$ip} 连接数超限\n";
            return false;
        }

        $this->connectionLimits[$request->fd] = $ip;
        return true;
    }

    private function validateAuth($request) {
        // 从查询参数或头部获取认证信息
        $token = $request->get['token'] ?? $request->header['authorization'] ?? '';

        if (empty($token)) {
            echo "拒绝连接: 缺少认证信息\n";
            return false;
        }

        // 简单的 token 验证（实际应用中应该更复杂）
        if (!$this->verifyToken($token)) {
            echo "拒绝连接: 无效的认证信息\n";
            return false;
        }

        return true;
    }

    private function verifyToken($token) {
        // 简化的 token 验证
        return $token === 'valid-token-123';
    }

    private function checkRateLimit($fd) {
        $maxMessages = 10; // 每分钟最多10条消息
        $timeWindow = 60;

        $now = time();

        if (!isset($this->rateLimits[$fd])) {
            $this->rateLimits[$fd] = [];
        }

        // 清理过期记录
        $this->rateLimits[$fd] = array_filter($this->rateLimits[$fd], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });

        if (count($this->rateLimits[$fd]) >= $maxMessages) {
            return false;
        }

        $this->rateLimits[$fd][] = $now;
        return true;
    }

    private function validateMessage($data) {
        $decoded = json_decode($data, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        // 检查必需字段
        if (!isset($decoded['type'])) {
            return false;
        }

        // 检查消息大小
        if (strlen($data) > 1024) { // 最大1KB
            return false;
        }

        return $decoded;
    }

    private function sanitizeMessage($data) {
        // 递归清理数据
        array_walk_recursive($data, function(&$value) {
            if (is_string($value)) {
                // 移除HTML标签
                $value = strip_tags($value);
                // 转义特殊字符
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                // 限制长度
                $value = substr($value, 0, 500);
            }
        });

        return $data;
    }

    private function handleSecureMessage($server, $fd, $data) {
        // 安全的消息处理逻辑
        switch ($data['type']) {
            case 'ping':
                $server->push($fd, json_encode(['type' => 'pong']));
                break;
            case 'message':
                // 处理用户消息
                $this->broadcastMessage($server, $fd, $data);
                break;
            default:
                $server->push($fd, json_encode(['error' => 'Unknown message type']));
                break;
        }
    }

    private function broadcastMessage($server, $senderFd, $data) {
        $message = [
            'type' => 'broadcast',
            'from' => $senderFd,
            'message' => $data['message'],
            'timestamp' => time()
        ];

        foreach ($server->connections as $fd) {
            if ($fd !== $senderFd && $server->isEstablished($fd)) {
                $server->push($fd, json_encode($message));
            }
        }
    }

    public function start() {
        echo "安全 WebSocket 服务器启动在 ws://0.0.0.0:9501\n";
        $this->server->start();
    }
}

$secureServer = new SecureWebSocketServer();
$secureServer->start();
?>
```

## 本章练习

### 练习 1：WebSocket 心跳机制
实现一个完整的心跳检测系统：

```php
<?php
// heartbeat_system.php

class HeartbeatWebSocketServer {
    // 实现功能：
    // 1. 客户端定期发送心跳
    // 2. 服务器检测连接状态
    // 3. 自动清理死连接
    // 4. 心跳统计和监控
}
?>
```

### 练习 2：WebSocket 消息队列
创建基于 WebSocket 的消息队列系统：

```php
<?php
// websocket_queue.php

class WebSocketMessageQueue {
    // 实现功能：
    // 1. 消息持久化
    // 2. 消息确认机制
    // 3. 消息重试
    // 4. 死信队列
    // 5. 消息优先级
}
?>
```

### 练习 3：WebSocket 集群
实现 WebSocket 服务器集群：

```php
<?php
// websocket_cluster.php

class WebSocketCluster {
    // 实现功能：
    // 1. 多服务器负载均衡
    // 2. 跨服务器消息广播
    // 3. 连接状态同步
    // 4. 故障转移
}
?>
```

### 练习 4：WebSocket 性能测试
开发 WebSocket 性能测试工具：

```php
<?php
// websocket_benchmark.php

class WebSocketBenchmark {
    // 实现功能：
    // 1. 并发连接测试
    // 2. 消息吞吐量测试
    // 3. 延迟测试
    // 4. 内存使用监控
    // 5. 性能报告生成
}
?>
```

## 本章小结

本章介绍了 WebSocket 的基础概念、协议原理和主要应用场景。通过详细的代码示例，我们了解了如何使用 Swoole 创建各种类型的 WebSocket 应用。

**关键要点：**

- **协议特性**：全双工通信、低延迟、持久连接
- **握手过程**：HTTP 升级到 WebSocket 的详细流程
- **应用场景**：聊天系统、实时数据推送、在线游戏
- **安全考虑**：来源验证、认证授权、消息过滤
- **性能优化**：连接管理、消息队列、集群部署

**设计原则：**

1. **安全第一**：验证来源、认证用户、过滤消息
2. **性能优化**：合理的连接限制和消息频率控制
3. **可扩展性**：支持集群部署和水平扩展
4. **可靠性**：心跳检测、异常处理、故障恢复
5. **用户体验**：低延迟、实时响应、稳定连接

**下一章预告：**
下一章我们将深入学习 WebSocket 服务器的开发，包括连接管理、消息路由、房间系统等高级特性。
```
