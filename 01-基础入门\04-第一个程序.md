# 第4章：第一个 Swoole 程序

## 学习目标
- 编写第一个 Swoole HTTP 服务器
- 理解 Swoole 程序的基本结构
- 掌握服务器配置和事件处理
- 学会调试和测试 Swoole 程序

## 4.1 Hello World 服务器

### 4.1.1 最简单的 HTTP 服务器

让我们从最简单的 HTTP 服务器开始：

```php
<?php
// hello_world.php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

// 创建 HTTP 服务器
$server = new Server("127.0.0.1", 9501);

// 处理 HTTP 请求
$server->on("request", function (Request $request, Response $response) {
    // 设置响应头
    $response->header("Content-Type", "text/html; charset=utf-8");
    
    // 返回响应内容
    $response->end("<h1>Hello Swoole!</h1><p>这是我的第一个 Swoole 程序</p>");
});

// 启动服务器
echo "HTTP 服务器启动成功\n";
echo "访问地址: http://127.0.0.1:9501\n";
echo "按 Ctrl+C 停止服务器\n";

$server->start();
?>
```

**运行程序：**
```bash
php hello_world.php
```

**测试访问：**
- 浏览器访问：http://127.0.0.1:9501
- 命令行测试：`curl http://127.0.0.1:9501`

### 4.1.2 添加更多事件处理

```php
<?php
// enhanced_server.php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

$server = new Server("127.0.0.1", 9501);

// 服务器启动事件
$server->on("start", function (Server $server) {
    echo "Swoole HTTP 服务器启动成功\n";
    echo "Master PID: {$server->master_pid}\n";
    echo "Manager PID: {$server->manager_pid}\n";
    echo "监听地址: http://127.0.0.1:9501\n";
});

// Worker 进程启动事件
$server->on("workerStart", function (Server $server, int $workerId) {
    echo "Worker #{$workerId} 启动，PID: " . getmypid() . "\n";
});

// 处理 HTTP 请求
$server->on("request", function (Request $request, Response $response) {
    $uri = $request->server['request_uri'];
    $method = $request->server['request_method'];
    
    echo "收到请求: {$method} {$uri}\n";
    
    // 设置响应头
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->header("Server", "Swoole-HTTP-Server");
    
    // 构造响应数据
    $data = [
        'message' => 'Hello Swoole!',
        'method' => $method,
        'uri' => $uri,
        'time' => date('Y-m-d H:i:s'),
        'worker_id' => $server->worker_id,
        'worker_pid' => getmypid(),
    ];
    
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
});

// 服务器关闭事件
$server->on("shutdown", function (Server $server) {
    echo "Swoole HTTP 服务器关闭\n";
});

$server->start();
?>
```

## 4.2 处理不同类型的请求

### 4.2.1 路由处理

```php
<?php
// router_server.php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function (Request $request, Response $response) {
    $uri = $request->server['request_uri'];
    $method = $request->server['request_method'];
    
    // 设置通用响应头
    $response->header("Content-Type", "application/json; charset=utf-8");
    
    // 简单路由处理
    switch ($uri) {
        case '/':
            handleHome($request, $response);
            break;
            
        case '/api/user':
            handleUser($request, $response);
            break;
            
        case '/api/time':
            handleTime($request, $response);
            break;
            
        case '/api/echo':
            handleEcho($request, $response);
            break;
            
        default:
            handle404($request, $response);
            break;
    }
});

// 首页处理
function handleHome(Request $request, Response $response) {
    $data = [
        'message' => '欢迎使用 Swoole API',
        'endpoints' => [
            'GET /api/user' => '获取用户信息',
            'GET /api/time' => '获取当前时间',
            'POST /api/echo' => '回显请求数据',
        ]
    ];
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 用户信息处理
function handleUser(Request $request, Response $response) {
    $data = [
        'id' => 1,
        'name' => '张三',
        'email' => '<EMAIL>',
        'created_at' => '2023-01-01 00:00:00'
    ];
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 时间处理
function handleTime(Request $request, Response $response) {
    $data = [
        'timestamp' => time(),
        'datetime' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get()
    ];
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 回显处理
function handleEcho(Request $request, Response $response) {
    $data = [
        'method' => $request->server['request_method'],
        'headers' => $request->header ?? [],
        'get' => $request->get ?? [],
        'post' => $request->post ?? [],
        'raw_content' => $request->rawContent(),
    ];
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 404 处理
function handle404(Request $request, Response $response) {
    $response->status(404);
    $data = [
        'error' => 'Not Found',
        'message' => '请求的资源不存在',
        'uri' => $request->server['request_uri']
    ];
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

echo "路由服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

### 4.2.2 处理 POST 请求和文件上传

```php
<?php
// post_server.php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function (Request $request, Response $response) {
    $response->header("Content-Type", "application/json; charset=utf-8");
    
    $uri = $request->server['request_uri'];
    $method = $request->server['request_method'];
    
    if ($uri === '/upload' && $method === 'POST') {
        handleFileUpload($request, $response);
    } elseif ($uri === '/form' && $method === 'POST') {
        handleFormData($request, $response);
    } elseif ($uri === '/json' && $method === 'POST') {
        handleJsonData($request, $response);
    } else {
        showUploadForm($request, $response);
    }
});

// 处理文件上传
function handleFileUpload(Request $request, Response $response) {
    if (empty($request->files)) {
        $response->status(400);
        $response->end(json_encode(['error' => '没有上传文件']));
        return;
    }
    
    $uploadDir = __DIR__ . '/uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $results = [];
    foreach ($request->files as $key => $file) {
        $filename = $uploadDir . basename($file['name']);
        
        if (move_uploaded_file($file['tmp_name'], $filename)) {
            $results[] = [
                'field' => $key,
                'original_name' => $file['name'],
                'size' => $file['size'],
                'type' => $file['type'],
                'saved_path' => $filename,
                'status' => 'success'
            ];
        } else {
            $results[] = [
                'field' => $key,
                'original_name' => $file['name'],
                'status' => 'failed'
            ];
        }
    }
    
    $response->end(json_encode(['uploads' => $results], JSON_PRETTY_PRINT));
}

// 处理表单数据
function handleFormData(Request $request, Response $response) {
    $data = [
        'post_data' => $request->post ?? [],
        'get_data' => $request->get ?? [],
        'received_at' => date('Y-m-d H:i:s')
    ];
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 处理 JSON 数据
function handleJsonData(Request $request, Response $response) {
    $rawContent = $request->rawContent();
    $jsonData = json_decode($rawContent, true);
    
    $data = [
        'raw_content' => $rawContent,
        'parsed_json' => $jsonData,
        'json_error' => json_last_error_msg(),
        'content_type' => $request->header['content-type'] ?? 'unknown'
    ];
    
    $response->end(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 显示上传表单
function showUploadForm(Request $request, Response $response) {
    $response->header("Content-Type", "text/html; charset=utf-8");
    
    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>Swoole 文件上传测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Swoole 文件上传测试</h1>
    
    <h2>文件上传</h2>
    <form action="/upload" method="post" enctype="multipart/form-data">
        <input type="file" name="file1" required><br><br>
        <input type="file" name="file2"><br><br>
        <button type="submit">上传文件</button>
    </form>
    
    <h2>表单数据</h2>
    <form action="/form" method="post">
        <input type="text" name="name" placeholder="姓名" required><br><br>
        <input type="email" name="email" placeholder="邮箱"><br><br>
        <textarea name="message" placeholder="消息"></textarea><br><br>
        <button type="submit">提交表单</button>
    </form>
    
    <h2>JSON 数据</h2>
    <form action="/json" method="post">
        <textarea name="json" placeholder=\'{"key": "value"}\' style="width:300px;height:100px;"></textarea><br><br>
        <button type="submit">发送 JSON</button>
    </form>
    
    <script>
    document.querySelector("form[action=\'/json\']").onsubmit = function(e) {
        e.preventDefault();
        const json = this.json.value;
        fetch("/json", {
            method: "POST",
            headers: {"Content-Type": "application/json"},
            body: json
        }).then(r => r.json()).then(data => {
            alert(JSON.stringify(data, null, 2));
        });
    };
    </script>
</body>
</html>';
    
    $response->end($html);
}

echo "POST 处理服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

## 4.3 服务器配置

### 4.3.1 基本配置选项

```php
<?php
// config_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 设置服务器配置
$server->set([
    // 进程配置
    'worker_num' => 4,              // Worker 进程数
    'task_worker_num' => 2,         // Task 进程数
    'max_request' => 10000,         // Worker 进程最大处理请求数

    // 网络配置
    'backlog' => 128,               // Listen 队列长度
    'max_conn' => 10000,            // 最大连接数
    'heartbeat_check_interval' => 60, // 心跳检测间隔
    'heartbeat_idle_time' => 600,   // 连接最大空闲时间

    // 缓冲区配置
    'package_max_length' => 2 * 1024 * 1024, // 数据包最大长度
    'buffer_output_size' => 2 * 1024 * 1024, // 输出缓冲区大小

    // 日志配置
    'log_file' => __DIR__ . '/swoole.log',
    'log_level' => SWOOLE_LOG_INFO,

    // 其他配置
    'daemonize' => false,           // 是否守护进程化
    'pid_file' => __DIR__ . '/server.pid',
]);

$server->on("request", function ($request, $response) {
    $info = [
        'worker_id' => $request->server['worker_id'] ?? 'unknown',
        'worker_pid' => getmypid(),
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true),
        'request_time' => date('Y-m-d H:i:s'),
    ];

    $response->header("Content-Type", "application/json");
    $response->end(json_encode($info, JSON_PRETTY_PRINT));
});

echo "配置服务器启动成功\n";
$server->start();
?>
```

### 4.3.2 性能优化配置

```php
<?php
// performance_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 性能优化配置
$server->set([
    // 进程配置优化
    'worker_num' => swoole_cpu_num() * 2,  // 根据 CPU 核心数设置
    'max_request' => 0,                     // 不自动重启 Worker
    'reload_async' => true,                 // 异步重启

    // 网络优化
    'open_tcp_nodelay' => true,            // 关闭 Nagle 算法
    'tcp_fastopen' => true,                // 启用 TCP Fast Open
    'open_cpu_affinity' => true,           // 启用 CPU 亲和性
    'tcp_defer_accept' => 5,               // TCP 延迟接收

    // 内存优化
    'buffer_output_size' => 32 * 1024 * 1024,  // 32MB 输出缓冲
    'socket_buffer_size' => 128 * 1024 * 1024, // 128MB Socket 缓冲

    // 协程配置
    'enable_coroutine' => true,
    'max_coroutine' => 100000,

    // 其他优化
    'enable_reuse_port' => true,           // 端口重用
    'enable_delay_receive' => true,        // 延迟接收
]);

$server->on("request", function ($request, $response) {
    // 模拟一些处理逻辑
    $start = microtime(true);

    // 模拟数据库查询
    usleep(rand(1000, 5000)); // 1-5ms

    $end = microtime(true);

    $data = [
        'message' => 'Performance optimized server',
        'process_time' => round(($end - $start) * 1000, 2) . 'ms',
        'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB',
        'worker_id' => $request->server['worker_id'] ?? 'unknown',
    ];

    $response->header("Content-Type", "application/json");
    $response->end(json_encode($data));
});

echo "性能优化服务器启动成功\n";
$server->start();
?>
```

## 4.4 调试和测试

### 4.4.1 调试技巧

```php
<?php
// debug_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 启用调试模式
$server->set([
    'log_level' => SWOOLE_LOG_DEBUG,
    'trace_flags' => SWOOLE_TRACE_ALL,
]);

// 添加调试信息
$server->on("request", function ($request, $response) {
    // 记录请求信息
    $debug_info = [
        'timestamp' => microtime(true),
        'method' => $request->server['request_method'],
        'uri' => $request->server['request_uri'],
        'headers' => $request->header,
        'get' => $request->get,
        'post' => $request->post,
        'server' => $request->server,
    ];

    // 输出调试信息到控制台
    echo "=== 请求调试信息 ===\n";
    echo json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    echo "==================\n";

    // 模拟错误处理
    try {
        if ($request->server['request_uri'] === '/error') {
            throw new Exception("模拟错误");
        }

        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['status' => 'success', 'debug' => $debug_info]));

    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
        echo "堆栈: " . $e->getTraceAsString() . "\n";

        $response->status(500);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ]));
    }
});

echo "调试服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "访问 http://127.0.0.1:9501/error 测试错误处理\n";
$server->start();
?>
```

### 4.4.2 性能测试

```php
<?php
// benchmark_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 统计信息
$stats = [
    'requests' => 0,
    'start_time' => time(),
    'memory_peak' => 0,
];

$server->on("request", function ($request, $response) use (&$stats) {
    $stats['requests']++;
    $current_memory = memory_get_usage(true);
    $stats['memory_peak'] = max($stats['memory_peak'], $current_memory);

    $uri = $request->server['request_uri'];

    if ($uri === '/stats') {
        // 返回统计信息
        $uptime = time() - $stats['start_time'];
        $qps = $uptime > 0 ? round($stats['requests'] / $uptime, 2) : 0;

        $data = [
            'total_requests' => $stats['requests'],
            'uptime_seconds' => $uptime,
            'qps' => $qps,
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
            'memory_peak' => round($stats['memory_peak'] / 1024 / 1024, 2) . 'MB',
            'worker_id' => $request->server['worker_id'] ?? 'unknown',
        ];

        $response->header("Content-Type", "application/json");
        $response->end(json_encode($data, JSON_PRETTY_PRINT));
    } else {
        // 普通响应
        $response->header("Content-Type", "text/plain");
        $response->end("Hello Swoole! Request #" . $stats['requests']);
    }
});

echo "性能测试服务器启动成功\n";
echo "访问 http://127.0.0.1:9501 进行测试\n";
echo "访问 http://127.0.0.1:9501/stats 查看统计信息\n";
echo "\n使用 ab 进行压力测试:\n";
echo "ab -n 10000 -c 100 http://127.0.0.1:9501/\n";

$server->start();
?>
```

## 本章练习

### 练习 1：创建个人信息 API
创建一个简单的个人信息管理 API，支持以下功能：
- GET /users - 获取用户列表
- GET /users/{id} - 获取指定用户信息
- POST /users - 创建新用户
- PUT /users/{id} - 更新用户信息
- DELETE /users/{id} - 删除用户

```php
<?php
// user_api.php

use Swoole\Http\Server;

// 模拟用户数据
$users = [
    1 => ['id' => 1, 'name' => '张三', 'email' => '<EMAIL>'],
    2 => ['id' => 2, 'name' => '李四', 'email' => '<EMAIL>'],
];

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use (&$users) {
    // 实现 RESTful API
    // 提示：解析 URI 和 HTTP 方法，实现 CRUD 操作
});

$server->start();
?>
```

### 练习 2：文件服务器
创建一个简单的文件服务器，支持文件上传、下载和列表功能：

```php
<?php
// file_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现文件服务器功能
    // 1. GET / - 显示文件列表
    // 2. GET /download/{filename} - 下载文件
    // 3. POST /upload - 上传文件
    // 4. DELETE /delete/{filename} - 删除文件
});

$server->start();
?>
```

### 练习 3：实时监控面板
创建一个实时监控面板，显示服务器状态：

```php
<?php
// monitor_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现监控面板
    // 1. 显示系统信息（CPU、内存、磁盘）
    // 2. 显示 Swoole 服务器状态
    // 3. 显示请求统计信息
    // 4. 提供 JSON API 和 HTML 界面
});

$server->start();
?>
```

### 练习 4：压力测试
编写脚本测试服务器在不同负载下的表现：

```bash
#!/bin/bash
# benchmark.sh

echo "开始压力测试..."

# 测试不同并发数
for concurrency in 10 50 100 200 500; do
    echo "测试并发数: $concurrency"
    ab -n 10000 -c $concurrency http://127.0.0.1:9501/ > "result_${concurrency}.txt"
    echo "完成并发数 $concurrency 的测试"
done

echo "压力测试完成，查看 result_*.txt 文件获取详细结果"
```

## 本章小结

本章通过编写第一个 Swoole 程序，让我们从实践中理解了 Swoole 的基本使用方法。我们学习了如何创建 HTTP 服务器、处理不同类型的请求、配置服务器参数以及调试和测试程序。

**关键要点：**
- Swoole HTTP 服务器的基本结构和事件处理
- 路由处理和请求响应机制
- 服务器配置和性能优化
- 调试技巧和性能测试方法
- 实际项目中的最佳实践

**下一章预告：**
下一章我们将深入学习 Swoole HTTP 服务器的高级特性，包括中间件、静态文件服务、会话管理等内容。
```
