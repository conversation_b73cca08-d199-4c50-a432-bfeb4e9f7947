# 第2章：安装与配置

## 学习目标
- 掌握 Swoole 的多种安装方法
- 了解 Swoole 的编译选项和配置参数
- 学会配置 PHP 和系统环境
- 掌握常见问题的解决方法

## 2.1 系统要求

### 2.1.1 操作系统支持
- **Linux**：推荐使用，性能最佳
  - Ubuntu 18.04+
  - CentOS 7+
  - Debian 9+
- **macOS**：开发环境可用
  - macOS 10.14+
- **Windows**：不推荐，仅用于学习

### 2.1.2 PHP 版本要求
- **PHP 7.2+**：最低要求
- **PHP 8.0+**：推荐版本
- **PHP 8.1+**：最新特性支持

### 2.1.3 系统依赖
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y \
    php-dev \
    php-pear \
    build-essential \
    libssl-dev \
    libcurl4-openssl-dev \
    libpcre3-dev \
    zlib1g-dev

# CentOS/RHEL
sudo yum install -y \
    php-devel \
    gcc \
    gcc-c++ \
    make \
    openssl-devel \
    curl-devel \
    pcre-devel \
    zlib-devel
```

## 2.2 安装方法

### 2.2.1 方法一：PECL 安装（推荐）

```bash
# 安装最新稳定版
sudo pecl install swoole

# 安装指定版本
sudo pecl install swoole-4.8.12

# 启用扩展
echo "extension=swoole.so" | sudo tee -a /etc/php/8.1/cli/php.ini
echo "extension=swoole.so" | sudo tee -a /etc/php/8.1/fpm/php.ini
```

### 2.2.2 方法二：源码编译安装

```bash
# 下载源码
wget https://github.com/swoole/swoole-src/archive/v4.8.12.tar.gz
tar -zxf v4.8.12.tar.gz
cd swoole-src-4.8.12

# 编译安装
phpize
./configure \
    --enable-openssl \
    --enable-http2 \
    --enable-swoole-curl \
    --enable-swoole-json \
    --enable-mysqlnd
make && sudo make install

# 启用扩展
echo "extension=swoole.so" | sudo tee -a $(php --ini | grep "Loaded Configuration" | sed -e "s|.*:\s*||")
```

### 2.2.3 方法三：Docker 安装

```dockerfile
# Dockerfile
FROM php:8.1-cli

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libssl-dev \
    libcurl4-openssl-dev \
    libpcre3-dev \
    zlib1g-dev

# 安装 Swoole
RUN pecl install swoole \
    && docker-php-ext-enable swoole

# 验证安装
RUN php -m | grep swoole
```

```bash
# 构建和运行
docker build -t php-swoole .
docker run -it php-swoole php -v
```

## 2.3 编译选项详解

### 2.3.1 核心选项

```bash
./configure \
    --enable-openssl          # 启用 SSL/TLS 支持
    --enable-http2           # 启用 HTTP/2 支持
    --enable-swoole-curl     # 启用协程 cURL 客户端
    --enable-swoole-json     # 启用 JSON 支持
    --enable-mysqlnd         # 启用 MySQL 原生驱动支持
```

### 2.3.2 可选功能

```bash
./configure \
    --enable-sockets         # 启用 sockets 支持
    --enable-swoole-sqlite   # 启用 SQLite 协程客户端
    --enable-swoole-pgsql    # 启用 PostgreSQL 协程客户端
    --with-jemalloc-dir      # 使用 jemalloc 内存分配器
    --enable-swoole-debug    # 启用调试模式（开发用）
```

### 2.3.3 性能优化选项

```bash
./configure \
    --with-jemalloc-dir=/usr/local/jemalloc  # jemalloc 优化内存分配
    --enable-swoole-static                   # 静态编译（减少依赖）
```

## 2.4 配置参数

### 2.4.1 php.ini 配置

```ini
; 基础配置
extension=swoole.so

; 协程配置
swoole.use_shortname = On                    ; 启用短名称
swoole.enable_coroutine = On                 ; 启用协程
swoole.enable_library = On                   ; 启用协程库
swoole.enable_preemptive_scheduler = Off     ; 抢占式调度器

; 性能配置
swoole.coroutine.max_num = 100000           ; 最大协程数量
swoole.coroutine.stack_size = 2097152       ; 协程栈大小 (2MB)

; 调试配置
swoole.display_errors = On                   ; 显示错误
swoole.trace_flags = 0                       ; 跟踪标志
```

### 2.4.2 运行时配置

```php
<?php
// runtime_config.php

// 设置协程相关配置
Swoole\Coroutine::set([
    'max_coroutine' => 100000,      // 最大协程数
    'stack_size' => 2 * 1024 * 1024, // 协程栈大小
    'log_level' => SWOOLE_LOG_INFO,   // 日志级别
    'trace_flags' => 0,               // 跟踪标志
]);

// 设置运行时钩子
Swoole\Runtime::enableCoroutine(SWOOLE_HOOK_ALL);
?>
```

## 2.5 验证安装

### 2.5.1 基础验证脚本

```php
<?php
// verify_installation.php

echo "=== Swoole 安装验证 ===\n\n";

// 检查扩展是否加载
if (!extension_loaded('swoole')) {
    echo "❌ Swoole 扩展未加载\n";
    exit(1);
}

echo "✅ Swoole 扩展已加载\n";
echo "版本: " . SWOOLE_VERSION . "\n\n";

// 检查编译选项
echo "=== 编译选项检查 ===\n";
$options = [
    'SWOOLE_SSL' => 'SSL/TLS 支持',
    'SWOOLE_HTTP2' => 'HTTP/2 支持',
    'SWOOLE_CURL' => 'cURL 支持',
    'SWOOLE_JSON' => 'JSON 支持',
];

foreach ($options as $constant => $description) {
    $status = defined($constant) ? "✅" : "❌";
    echo "{$status} {$description}\n";
}

// 检查协程支持
echo "\n=== 协程支持检查 ===\n";
if (class_exists('Swoole\Coroutine')) {
    echo "✅ 协程类可用\n";
    
    // 测试协程创建
    use function Swoole\Coroutine\run;
    
    run(function () {
        echo "✅ 协程运行正常\n";
    });
} else {
    echo "❌ 协程类不可用\n";
}

echo "\n=== 安装验证完成 ===\n";
?>
```

### 2.5.2 功能测试脚本

```php
<?php
// function_test.php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

echo "启动 HTTP 服务器测试...\n";

$server = new Server("127.0.0.1", 9501);

$server->on("request", function (Request $request, Response $response) {
    $response->header("Content-Type", "text/plain");
    $response->end("Hello Swoole! 安装成功！");
});

$server->on("start", function ($server) {
    echo "HTTP 服务器启动成功\n";
    echo "访问地址: http://127.0.0.1:9501\n";
    echo "按 Ctrl+C 停止服务器\n";
});

$server->start();
?>
```

## 2.6 常见问题解决

### 2.6.1 编译错误

**问题：找不到 php-config**
```bash
# 解决方案
sudo apt-get install php-dev  # Ubuntu/Debian
sudo yum install php-devel     # CentOS/RHEL
```

**问题：OpenSSL 相关错误**
```bash
# 解决方案
sudo apt-get install libssl-dev      # Ubuntu/Debian
sudo yum install openssl-devel        # CentOS/RHEL
```

### 2.6.2 运行时错误

**问题：协程不可用**
```php
// 检查协程是否启用
if (!Swoole\Coroutine::getCid()) {
    echo "协程未启用，请检查配置\n";
}

// 启用协程钩子
Swoole\Runtime::enableCoroutine(SWOOLE_HOOK_ALL);
```

**问题：端口被占用**
```bash
# 查找占用端口的进程
sudo netstat -tlnp | grep :9501
sudo lsof -i :9501

# 杀死进程
sudo kill -9 <PID>
```

### 2.6.3 性能问题

**问题：内存不足**
```ini
; 增加内存限制
memory_limit = 512M

; 调整协程栈大小
swoole.coroutine.stack_size = 8192
```

**问题：文件描述符不足**
```bash
# 临时调整
ulimit -n 65535

# 永久调整 /etc/security/limits.conf
* soft nofile 65535
* hard nofile 65535
```

## 2.7 开发环境配置

### 2.7.1 IDE 配置

**PhpStorm 配置：**
1. 安装 Swoole IDE Helper
```bash
composer require --dev swoole/ide-helper
```

2. 配置代码提示
```php
// .phpstorm.meta.php
<?php
namespace PHPSTORM_META {
    registerArgumentsSet('swoole_hook_flags',
        SWOOLE_HOOK_TCP,
        SWOOLE_HOOK_UDP,
        SWOOLE_HOOK_UNIX,
        SWOOLE_HOOK_UDG,
        SWOOLE_HOOK_SSL,
        SWOOLE_HOOK_TLS,
        SWOOLE_HOOK_STREAM_FUNCTION,
        SWOOLE_HOOK_FILE,
        SWOOLE_HOOK_SLEEP,
        SWOOLE_HOOK_PROC,
        SWOOLE_HOOK_CURL,
        SWOOLE_HOOK_NATIVE_CURL,
        SWOOLE_HOOK_BLOCKING_FUNCTION,
        SWOOLE_HOOK_ALL
    );
}
?>
```

### 2.7.2 调试配置

```php
<?php
// debug_config.php

// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置日志级别
Swoole\Coroutine::set([
    'log_level' => SWOOLE_LOG_DEBUG,
    'trace_flags' => SWOOLE_TRACE_ALL,
]);

// 启用调试模式
if (defined('SWOOLE_DEBUG')) {
    echo "调试模式已启用\n";
}
?>
```

## 本章练习

### 练习 1：多种安装方式实践
尝试使用不同的方式安装 Swoole：
1. 使用 PECL 安装
2. 使用源码编译安装
3. 使用 Docker 安装

### 练习 2：配置优化
根据你的系统配置，优化 Swoole 参数：
1. 调整协程数量限制
2. 优化内存使用
3. 配置日志级别

### 练习 3：问题排查
模拟并解决常见安装问题：
1. 端口占用问题
2. 权限问题
3. 依赖缺失问题

### 练习 4：性能测试
编写脚本测试不同配置下的性能差异：

```php
<?php
// performance_test.php

use function Swoole\Coroutine\run;
use Swoole\Coroutine;

// 测试不同协程数量的性能
function testCoroutinePerformance($count) {
    $start = microtime(true);
    
    run(function () use ($count) {
        for ($i = 0; $i < $count; $i++) {
            Coroutine::create(function () {
                Coroutine::sleep(0.001); // 1ms
            });
        }
    });
    
    $end = microtime(true);
    echo "创建 {$count} 个协程耗时: " . ($end - $start) . " 秒\n";
}

// 测试不同数量
testCoroutinePerformance(1000);
testCoroutinePerformance(10000);
testCoroutinePerformance(100000);
?>
```

## 本章小结

本章详细介绍了 Swoole 的安装和配置方法，包括系统要求、多种安装方式、编译选项、配置参数等。正确的安装和配置是使用 Swoole 的基础，建议在实际项目中根据具体需求进行相应的优化配置。

**关键要点：**
- 选择合适的安装方式（PECL 推荐）
- 根据需求选择编译选项
- 合理配置运行时参数
- 掌握常见问题的解决方法

**下一章预告：**
下一章我们将学习 Swoole 的基本概念和核心特性，为深入使用 Swoole 打下理论基础。
