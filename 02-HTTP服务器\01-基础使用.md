# 第5章：HTTP 服务器基础

## 学习目标
- 深入理解 Swoole HTTP 服务器的工作原理
- 掌握 HTTP 服务器的配置和优化
- 学会处理各种 HTTP 特性
- 了解与传统 Web 服务器的区别

## 5.1 HTTP 服务器架构

### 5.1.1 Swoole HTTP 服务器特点

Swoole HTTP 服务器是一个完整的 HTTP 服务器实现，具有以下特点：

- **高性能**：基于事件驱动和协程
- **内置支持**：无需 Nginx/Apache
- **协议完整**：支持 HTTP/1.1 和 HTTP/2
- **功能丰富**：支持 WebSocket 升级、静态文件、压缩等

```php
<?php
// basic_http_server.php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

$server = new Server("0.0.0.0", 9501);

// 基础配置
$server->set([
    'worker_num' => swoole_cpu_num(),
    'enable_static_handler' => true,    // 启用静态文件处理
    'document_root' => __DIR__ . '/public',
    'enable_gzip' => true,              // 启用 Gzip 压缩
    'http_compression' => true,
    'compression_level' => 6,
]);

$server->on("request", function (Request $request, Response $response) {
    $response->header("Content-Type", "application/json");
    $response->header("Server", "Swoole-HTTP-Server");
    
    $data = [
        'message' => 'Swoole HTTP Server',
        'version' => SWOOLE_VERSION,
        'time' => date('Y-m-d H:i:s'),
        'request_info' => [
            'method' => $request->server['request_method'],
            'uri' => $request->server['request_uri'],
            'protocol' => $request->server['server_protocol'],
        ]
    ];
    
    $response->end(json_encode($data, JSON_PRETTY_PRINT));
});

echo "HTTP 服务器启动在 http://0.0.0.0:9501\n";
$server->start();
?>
```

### 5.1.2 与传统 Web 服务器的对比

| 特性 | 传统 Web 服务器 | Swoole HTTP 服务器 |
|------|----------------|-------------------|
| 进程模型 | 每请求一进程/线程 | 多进程 + 协程 |
| 内存使用 | 高（进程隔离） | 低（共享内存） |
| 启动速度 | 慢（每次加载） | 快（常驻内存） |
| 并发能力 | 受限于进程数 | 高（协程支持） |
| 开发调试 | 简单（无状态） | 需要注意内存泄漏 |

## 5.2 请求和响应处理

### 5.2.1 Request 对象详解

```php
<?php
// request_analysis.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 分析请求对象的所有属性
    $analysis = [
        // 服务器信息
        'server' => $request->server,
        
        // 请求头
        'headers' => $request->header ?? [],
        
        // GET 参数
        'get_params' => $request->get ?? [],
        
        // POST 参数
        'post_params' => $request->post ?? [],
        
        // Cookie
        'cookies' => $request->cookie ?? [],
        
        // 上传文件
        'files' => $request->files ?? [],
        
        // 原始请求体
        'raw_content' => $request->rawContent(),
        
        // 临时文件
        'tmp_files' => $request->tmpfiles ?? [],
    ];
    
    // 特殊处理 JSON 请求
    $content_type = $request->header['content-type'] ?? '';
    if (strpos($content_type, 'application/json') !== false) {
        $analysis['json_data'] = json_decode($request->rawContent(), true);
    }
    
    $response->header("Content-Type", "application/json; charset=utf-8");
    $response->end(json_encode($analysis, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
});

echo "请求分析服务器启动成功\n";
$server->start();
?>
```

### 5.2.2 Response 对象详解

```php
<?php
// response_demo.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];
    
    switch ($uri) {
        case '/json':
            handleJsonResponse($response);
            break;
            
        case '/html':
            handleHtmlResponse($response);
            break;
            
        case '/redirect':
            handleRedirect($response);
            break;
            
        case '/cookie':
            handleCookie($response);
            break;
            
        case '/download':
            handleFileDownload($response);
            break;
            
        case '/stream':
            handleStreamResponse($response);
            break;
            
        default:
            handleDefault($response);
            break;
    }
});

function handleJsonResponse($response) {
    $response->header("Content-Type", "application/json");
    $response->header("X-Custom-Header", "Custom Value");
    
    $data = [
        'type' => 'json',
        'timestamp' => time(),
        'data' => ['key1' => 'value1', 'key2' => 'value2']
    ];
    
    $response->end(json_encode($data));
}

function handleHtmlResponse($response) {
    $response->header("Content-Type", "text/html; charset=utf-8");
    
    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>Swoole HTTP Response</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>HTML 响应示例</h1>
    <p>当前时间: ' . date('Y-m-d H:i:s') . '</p>
    <ul>
        <li><a href="/json">JSON 响应</a></li>
        <li><a href="/redirect">重定向</a></li>
        <li><a href="/cookie">设置 Cookie</a></li>
        <li><a href="/download">文件下载</a></li>
        <li><a href="/stream">流式响应</a></li>
    </ul>
</body>
</html>';
    
    $response->end($html);
}

function handleRedirect($response) {
    $response->status(302);
    $response->header("Location", "/html");
    $response->end("Redirecting...");
}

function handleCookie($response) {
    // 设置 Cookie
    $response->cookie("user_id", "12345", time() + 3600, "/", "", false, true);
    $response->cookie("session_token", uniqid(), time() + 7200);
    
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'message' => 'Cookie 已设置',
        'cookies' => [
            'user_id' => '12345',
            'session_token' => 'generated'
        ]
    ]));
}

function handleFileDownload($response) {
    $filename = "example.txt";
    $content = "这是一个示例文件内容\n生成时间: " . date('Y-m-d H:i:s');
    
    $response->header("Content-Type", "application/octet-stream");
    $response->header("Content-Disposition", "attachment; filename=\"{$filename}\"");
    $response->header("Content-Length", strlen($content));
    
    $response->end($content);
}

function handleStreamResponse($response) {
    $response->header("Content-Type", "text/plain");
    $response->header("Transfer-Encoding", "chunked");
    
    // 分块发送数据
    for ($i = 1; $i <= 5; $i++) {
        $chunk = "数据块 {$i}: " . date('Y-m-d H:i:s') . "\n";
        $response->write($chunk);
        
        // 模拟处理延迟
        usleep(500000); // 0.5 秒
    }
    
    $response->end("流式响应完成\n");
}

function handleDefault($response) {
    $response->header("Content-Type", "text/html; charset=utf-8");
    
    $html = '
<!DOCTYPE html>
<html>
<head>
    <title>Swoole HTTP 服务器</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>欢迎使用 Swoole HTTP 服务器</h1>
    <h2>可用的演示端点：</h2>
    <ul>
        <li><a href="/json">/json</a> - JSON 响应</li>
        <li><a href="/html">/html</a> - HTML 响应</li>
        <li><a href="/redirect">/redirect</a> - 重定向</li>
        <li><a href="/cookie">/cookie</a> - 设置 Cookie</li>
        <li><a href="/download">/download</a> - 文件下载</li>
        <li><a href="/stream">/stream</a> - 流式响应</li>
    </ul>
</body>
</html>';
    
    $response->end($html);
}

echo "响应演示服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

## 5.3 HTTP 协议特性

### 5.3.1 HTTP/2 支持

```php
<?php
// http2_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501, SWOOLE_PROCESS, SWOOLE_SOCK_TCP | SWOOLE_SSL);

// HTTP/2 需要 SSL 支持
$server->set([
    'ssl_cert_file' => __DIR__ . '/ssl/server.crt',
    'ssl_key_file' => __DIR__ . '/ssl/server.key',
    'open_http2_protocol' => true,  // 启用 HTTP/2
    'http_compression' => true,
]);

$server->on("request", function ($request, $response) {
    // 检查协议版本
    $protocol = $request->server['server_protocol'] ?? 'HTTP/1.1';
    
    $response->header("Content-Type", "application/json");
    $response->end(json_encode([
        'protocol' => $protocol,
        'http2' => $protocol === 'HTTP/2',
        'message' => 'HTTP/2 服务器响应',
        'features' => [
            'multiplexing' => true,
            'server_push' => true,
            'header_compression' => true,
        ]
    ], JSON_PRETTY_PRINT));
});

echo "HTTP/2 服务器启动成功，访问 https://127.0.0.1:9501\n";
$server->start();
?>
```

### 5.3.2 WebSocket 升级

```php
<?php
// websocket_upgrade.php

use Swoole\WebSocket\Server;

$server = new Server("127.0.0.1", 9501);

// 处理 HTTP 请求
$server->on('request', function ($request, $response) {
    if ($request->server['request_uri'] === '/') {
        $html = '
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket 测试</title>
</head>
<body>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="输入消息">
    <button onclick="sendMessage()">发送</button>
    
    <script>
        const ws = new WebSocket("ws://127.0.0.1:9501");
        const messages = document.getElementById("messages");
        
        ws.onmessage = function(event) {
            messages.innerHTML += "<p>收到: " + event.data + "</p>";
        };
        
        function sendMessage() {
            const input = document.getElementById("messageInput");
            ws.send(input.value);
            input.value = "";
        }
    </script>
</body>
</html>';
        
        $response->header("Content-Type", "text/html; charset=utf-8");
        $response->end($html);
    } else {
        $response->status(404);
        $response->end("Not Found");
    }
});

// 处理 WebSocket 连接
$server->on('open', function ($server, $request) {
    echo "WebSocket 连接建立: {$request->fd}\n";
});

$server->on('message', function ($server, $frame) {
    echo "收到消息: {$frame->data}\n";
    $server->push($frame->fd, "服务器回复: " . $frame->data);
});

$server->on('close', function ($server, $fd) {
    echo "WebSocket 连接关闭: {$fd}\n";
});

echo "WebSocket 服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

## 5.4 性能优化配置

### 5.4.1 连接池和缓冲区优化

```php
<?php
// optimized_server.php

use Swoole\Http\Server;

$server = new Server("0.0.0.0", 9501);

$server->set([
    // 进程配置
    'worker_num' => swoole_cpu_num() * 2,
    'max_request' => 0,                    // 不重启 Worker
    'reload_async' => true,

    // 网络优化
    'backlog' => 128,
    'max_conn' => 10000,
    'heartbeat_check_interval' => 60,
    'heartbeat_idle_time' => 600,

    // 缓冲区优化
    'package_max_length' => 8 * 1024 * 1024,      // 8MB
    'buffer_output_size' => 32 * 1024 * 1024,     // 32MB
    'socket_buffer_size' => 128 * 1024 * 1024,    // 128MB

    // HTTP 特定优化
    'http_parse_post' => true,
    'http_parse_cookie' => true,
    'http_compression' => true,
    'compression_level' => 6,
    'http_gzip_level' => 6,

    // 静态文件优化
    'enable_static_handler' => true,
    'document_root' => __DIR__ . '/public',
    'static_handler_locations' => ['/static', '/assets', '/uploads'],

    // 协程优化
    'enable_coroutine' => true,
    'max_coroutine' => 100000,

    // 其他优化
    'open_tcp_nodelay' => true,
    'tcp_fastopen' => true,
    'open_cpu_affinity' => true,
    'enable_reuse_port' => true,
]);

$server->on("request", function ($request, $response) {
    // 性能监控
    $start_time = microtime(true);
    $start_memory = memory_get_usage();

    // 模拟业务处理
    $data = [
        'message' => 'Optimized Swoole Server',
        'timestamp' => time(),
        'worker_id' => $request->server['worker_id'] ?? 'unknown',
        'worker_pid' => getmypid(),
    ];

    // 计算性能指标
    $end_time = microtime(true);
    $end_memory = memory_get_usage();

    $data['performance'] = [
        'execution_time' => round(($end_time - $start_time) * 1000, 2) . 'ms',
        'memory_used' => round(($end_memory - $start_memory) / 1024, 2) . 'KB',
        'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB',
    ];

    $response->header("Content-Type", "application/json");
    $response->end(json_encode($data, JSON_PRETTY_PRINT));
});

echo "优化服务器启动成功\n";
$server->start();
?>
```

### 5.4.2 内存管理和监控

```php
<?php
// memory_monitor.php

use Swoole\Http\Server;
use Swoole\Timer;

$server = new Server("127.0.0.1", 9501);

// 全局统计信息
$stats = [
    'requests' => 0,
    'start_time' => time(),
    'memory_peak' => 0,
];

$server->set([
    'worker_num' => 2,
    'max_request' => 1000,  // 定期重启防止内存泄漏
]);

$server->on("workerStart", function ($server, $workerId) {
    // 定时监控内存使用
    Timer::tick(5000, function () use (&$stats) {
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);

        echo "Worker " . getmypid() . " 内存使用: " .
             round($memory_usage / 1024 / 1024, 2) . "MB, " .
             "峰值: " . round($memory_peak / 1024 / 1024, 2) . "MB\n";

        // 内存使用过高时警告
        if ($memory_usage > 100 * 1024 * 1024) { // 100MB
            echo "警告: Worker 内存使用过高!\n";
        }
    });
});

$server->on("request", function ($request, $response) use (&$stats) {
    $stats['requests']++;

    $uri = $request->server['request_uri'];

    if ($uri === '/stats') {
        // 返回统计信息
        $uptime = time() - $stats['start_time'];

        $data = [
            'server_stats' => [
                'uptime' => $uptime,
                'total_requests' => $stats['requests'],
                'qps' => $uptime > 0 ? round($stats['requests'] / $uptime, 2) : 0,
            ],
            'memory_stats' => [
                'current_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'peak_usage' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB',
                'limit' => ini_get('memory_limit'),
            ],
            'process_stats' => [
                'worker_id' => $request->server['worker_id'] ?? 'unknown',
                'worker_pid' => getmypid(),
                'parent_pid' => getmypid(),
            ]
        ];

        $response->header("Content-Type", "application/json");
        $response->end(json_encode($data, JSON_PRETTY_PRINT));
    } elseif ($uri === '/gc') {
        // 手动触发垃圾回收
        $before = memory_get_usage(true);
        gc_collect_cycles();
        $after = memory_get_usage(true);

        $data = [
            'message' => '垃圾回收完成',
            'memory_before' => round($before / 1024 / 1024, 2) . 'MB',
            'memory_after' => round($after / 1024 / 1024, 2) . 'MB',
            'freed' => round(($before - $after) / 1024 / 1024, 2) . 'MB',
        ];

        $response->header("Content-Type", "application/json");
        $response->end(json_encode($data, JSON_PRETTY_PRINT));
    } else {
        // 普通请求
        $response->header("Content-Type", "text/html; charset=utf-8");
        $response->end('
<!DOCTYPE html>
<html>
<head>
    <title>内存监控</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Swoole 内存监控</h1>
    <ul>
        <li><a href="/stats">查看统计信息</a></li>
        <li><a href="/gc">手动垃圾回收</a></li>
    </ul>
    <p>当前时间: ' . date('Y-m-d H:i:s') . '</p>
</body>
</html>');
    }
});

echo "内存监控服务器启动成功，访问 http://127.0.0.1:9501\n";
$server->start();
?>
```

## 本章练习

### 练习 1：HTTP 头部分析器
创建一个 HTTP 头部分析器，详细分析客户端发送的所有头部信息：

```php
<?php
// header_analyzer.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现功能：
    // 1. 分析所有 HTTP 头部
    // 2. 识别浏览器类型和版本
    // 3. 检测客户端支持的功能（压缩、缓存等）
    // 4. 分析 Accept 头部
    // 5. 提供 JSON 和 HTML 两种输出格式
});

$server->start();
?>
```

### 练习 2：文件上传服务器
实现一个功能完整的文件上传服务器：

```php
<?php
// upload_server.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现功能：
    // 1. 支持单文件和多文件上传
    // 2. 文件类型验证
    // 3. 文件大小限制
    // 4. 生成唯一文件名
    // 5. 上传进度显示
    // 6. 文件列表和删除功能
});

$server->start();
?>
```

### 练习 3：API 限流器
实现一个简单的 API 限流器：

```php
<?php
// rate_limiter.php

use Swoole\Http\Server;
use Swoole\Table;

$server = new Server("127.0.0.1", 9501);

// 创建限流表
$rateLimit = new Table(1024);
$rateLimit->column('count', Table::TYPE_INT);
$rateLimit->column('reset_time', Table::TYPE_INT);
$rateLimit->create();

$server->on("request", function ($request, $response) use ($rateLimit) {
    // 实现功能：
    // 1. 基于 IP 的限流
    // 2. 可配置的限流规则
    // 3. 限流状态返回
    // 4. 重置时间计算
    // 5. 白名单支持
});

$server->start();
?>
```

### 练习 4：性能基准测试
创建一个性能基准测试工具：

```php
<?php
// benchmark.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    // 实现功能：
    // 1. 不同负载的性能测试
    // 2. 内存使用监控
    // 3. 响应时间统计
    // 4. 并发连接数监控
    // 5. 生成性能报告
});

$server->start();
?>
```

## 本章小结

本章深入介绍了 Swoole HTTP 服务器的基础使用方法，包括服务器架构、请求响应处理、HTTP 协议特性和性能优化。通过大量的实例代码，我们了解了如何构建高性能的 HTTP 服务器。

**关键要点：**
- Swoole HTTP 服务器的架构优势和特点
- Request 和 Response 对象的详细使用
- HTTP/2 和 WebSocket 升级支持
- 性能优化配置和内存管理
- 实际开发中的最佳实践

**下一章预告：**
下一章我们将学习如何处理复杂的请求和响应，包括文件上传、流式处理、缓存控制等高级特性。
```
