# 第15章：多进程架构

## 学习目标
- 理解 Swoole 的多进程架构设计
- 掌握进程类型和职责分工
- 学会进程配置和管理
- 了解进程间通信机制

## 15.1 Swoole 进程架构概述

### 15.1.1 进程架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        Master 进程                          │
│                    (主进程，负责管理)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ├─── Manager 进程 (管理进程)
                      │    ├─── Worker 进程 1 (工作进程)
                      │    ├─── Worker 进程 2
                      │    ├─── Worker 进程 N
                      │    ├─── Task Worker 进程 1 (任务进程)
                      │    ├─── Task Worker 进程 2
                      │    └─── Task Worker 进程 M
                      │
                      └─── Reactor 线程组 (事件循环)
                           ├─── Reactor 线程 1
                           ├─── Reactor 线程 2
                           └─── Reactor 线程 K
```

### 15.1.2 进程架构详解

```php
<?php
// process_architecture.php

use Swoole\Server;
use Swoole\Process;

class ProcessArchitectureDemo {
    private $server;
    
    public function __construct() {
        $this->server = new Server('0.0.0.0', 9501);
        $this->setupServer();
        $this->setupEvents();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 4,           // Worker 进程数
            'task_worker_num' => 2,      // Task Worker 进程数
            'reactor_num' => 2,          // Reactor 线程数
            'max_conn' => 10000,         // 最大连接数
            'daemonize' => false,        // 是否守护进程化
            'log_file' => '/tmp/swoole.log',
            'log_level' => SWOOLE_LOG_INFO,
        ]);
    }
    
    private function setupEvents() {
        // Master 进程事件
        $this->server->on('start', [$this, 'onMasterStart']);
        $this->server->on('shutdown', [$this, 'onMasterShutdown']);
        
        // Manager 进程事件
        $this->server->on('managerStart', [$this, 'onManagerStart']);
        $this->server->on('managerStop', [$this, 'onManagerStop']);
        
        // Worker 进程事件
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('workerStop', [$this, 'onWorkerStop']);
        $this->server->on('workerError', [$this, 'onWorkerError']);
        
        // 业务事件
        $this->server->on('connect', [$this, 'onConnect']);
        $this->server->on('receive', [$this, 'onReceive']);
        $this->server->on('close', [$this, 'onClose']);
        
        // 任务事件
        $this->server->on('task', [$this, 'onTask']);
        $this->server->on('finish', [$this, 'onFinish']);
    }
    
    // Master 进程事件
    public function onMasterStart($server) {
        echo "=== Master 进程启动 ===\n";
        echo "Master PID: {$server->master_pid}\n";
        echo "Manager PID: {$server->manager_pid}\n";
        echo "监听地址: {$server->host}:{$server->port}\n";
        
        // 设置进程名称
        cli_set_process_title("swoole-master");
        
        // Master 进程主要职责：
        // 1. 创建 Manager 进程
        // 2. 创建 Reactor 线程组
        // 3. 监听端口
        // 4. 接受新连接
        
        echo "Master 进程职责:\n";
        echo "- 创建和管理 Manager 进程\n";
        echo "- 创建 Reactor 线程组\n";
        echo "- 监听端口，接受连接\n";
        echo "- 信号处理\n\n";
    }
    
    public function onMasterShutdown($server) {
        echo "=== Master 进程关闭 ===\n";
        echo "服务器已停止\n";
    }
    
    // Manager 进程事件
    public function onManagerStart($server) {
        echo "=== Manager 进程启动 ===\n";
        echo "Manager PID: " . getmypid() . "\n";
        
        // 设置进程名称
        cli_set_process_title("swoole-manager");
        
        // Manager 进程主要职责：
        // 1. 创建和管理 Worker 进程
        // 2. 创建和管理 Task Worker 进程
        // 3. 监控子进程状态
        // 4. 重启异常退出的子进程
        
        echo "Manager 进程职责:\n";
        echo "- 创建和管理 Worker 进程\n";
        echo "- 创建和管理 Task Worker 进程\n";
        echo "- 监控子进程状态\n";
        echo "- 重启异常退出的进程\n\n";
    }
    
    public function onManagerStop($server) {
        echo "=== Manager 进程停止 ===\n";
    }
    
    // Worker 进程事件
    public function onWorkerStart($server, $workerId) {
        $processType = $workerId >= $server->setting['worker_num'] ? 'Task Worker' : 'Worker';
        
        echo "=== {$processType} 进程启动 ===\n";
        echo "Worker ID: {$workerId}\n";
        echo "Process PID: " . getmypid() . "\n";
        
        if ($workerId >= $server->setting['worker_num']) {
            // Task Worker 进程
            cli_set_process_title("swoole-task-worker-{$workerId}");
            
            echo "Task Worker 进程职责:\n";
            echo "- 处理异步任务\n";
            echo "- 执行耗时操作\n";
            echo "- 处理定时任务\n";
            echo "- 与 Worker 进程通信\n\n";
        } else {
            // Worker 进程
            cli_set_process_title("swoole-worker-{$workerId}");
            
            echo "Worker 进程职责:\n";
            echo "- 处理客户端请求\n";
            echo "- 执行业务逻辑\n";
            echo "- 投递异步任务\n";
            echo "- 维护连接状态\n\n";
            
            // 在 Worker 进程中设置定时器
            if ($workerId === 0) {
                \Swoole\Timer::tick(5000, function() use ($workerId) {
                    echo "[Worker {$workerId}] 定时器执行，时间: " . date('Y-m-d H:i:s') . "\n";
                });
            }
        }
    }
    
    public function onWorkerStop($server, $workerId) {
        echo "Worker {$workerId} 进程停止\n";
    }
    
    public function onWorkerError($server, $workerId, $workerPid, $exitCode, $signal) {
        echo "Worker 进程异常退出:\n";
        echo "Worker ID: {$workerId}\n";
        echo "Process PID: {$workerPid}\n";
        echo "Exit Code: {$exitCode}\n";
        echo "Signal: {$signal}\n";
    }
    
    // 业务事件
    public function onConnect($server, $fd, $reactorId) {
        $workerId = $server->getWorkerId();
        echo "[Worker {$workerId}] 新连接: FD={$fd}, Reactor={$reactorId}\n";
        
        // 获取连接信息
        $clientInfo = $server->getClientInfo($fd);
        echo "客户端信息: {$clientInfo['remote_ip']}:{$clientInfo['remote_port']}\n";
    }
    
    public function onReceive($server, $fd, $reactorId, $data) {
        $workerId = $server->getWorkerId();
        echo "[Worker {$workerId}] 收到数据: FD={$fd}, 长度=" . strlen($data) . "\n";
        
        $message = trim($data);
        
        // 根据消息类型处理
        if (strpos($message, 'task:') === 0) {
            // 投递异步任务
            $taskData = substr($message, 5);
            $taskId = $server->task($taskData);
            $server->send($fd, "任务已投递，Task ID: {$taskId}\n");
        } elseif ($message === 'info') {
            // 返回进程信息
            $info = [
                'worker_id' => $workerId,
                'worker_pid' => getmypid(),
                'memory_usage' => memory_get_usage(true),
                'connections' => count($server->connections),
            ];
            $server->send($fd, json_encode($info, JSON_PRETTY_PRINT) . "\n");
        } elseif ($message === 'stats') {
            // 返回服务器统计信息
            $stats = $server->stats();
            $server->send($fd, json_encode($stats, JSON_PRETTY_PRINT) . "\n");
        } else {
            // 回显消息
            $server->send($fd, "[Worker {$workerId}] Echo: {$message}\n");
        }
    }
    
    public function onClose($server, $fd, $reactorId) {
        $workerId = $server->getWorkerId();
        echo "[Worker {$workerId}] 连接关闭: FD={$fd}\n";
    }
    
    // 任务事件
    public function onTask($server, $taskId, $reactorId, $data) {
        $workerId = $server->getWorkerId();
        echo "[Task Worker {$workerId}] 处理任务: Task ID={$taskId}\n";
        echo "任务数据: {$data}\n";
        
        // 模拟耗时操作
        sleep(2);
        
        $result = [
            'task_id' => $taskId,
            'worker_id' => $workerId,
            'data' => $data,
            'result' => 'Task completed at ' . date('Y-m-d H:i:s'),
            'memory_usage' => memory_get_usage(true)
        ];
        
        echo "[Task Worker {$workerId}] 任务完成: Task ID={$taskId}\n";
        
        return $result;
    }
    
    public function onFinish($server, $taskId, $data) {
        $workerId = $server->getWorkerId();
        echo "[Worker {$workerId}] 任务结果: Task ID={$taskId}\n";
        echo "结果数据: " . json_encode($data) . "\n";
    }
    
    public function start() {
        echo "启动多进程架构演示服务器...\n\n";
        $this->server->start();
    }
}

$demo = new ProcessArchitectureDemo();
$demo->start();
?>
```

## 15.2 进程类型详解

### 15.2.1 Master 进程

Master 进程是整个服务器的主进程，负责：

1. **进程管理**：创建和管理 Manager 进程
2. **网络监听**：监听端口，接受新连接
3. **Reactor 管理**：创建和管理 Reactor 线程组
4. **信号处理**：处理系统信号，如 SIGTERM、SIGUSR1 等

```php
<?php
// master_process.php

use Swoole\Server;

class MasterProcessDemo {
    private $server;
    
    public function __construct() {
        $this->server = new Server('0.0.0.0', 9501);
        $this->setupServer();
        $this->setupSignalHandlers();
        $this->setupEvents();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 2,
            'task_worker_num' => 1,
            'daemonize' => false,
            'pid_file' => '/tmp/swoole_master.pid',
            'log_file' => '/tmp/swoole_master.log',
        ]);
    }
    
    private function setupSignalHandlers() {
        // 注意：信号处理器只能在 Master 进程中注册
        Process::signal(SIGTERM, function($signo) {
            echo "收到 SIGTERM 信号，准备关闭服务器...\n";
            $this->server->shutdown();
        });
        
        Process::signal(SIGUSR1, function($signo) {
            echo "收到 SIGUSR1 信号，重新加载配置...\n";
            $this->reloadWorkers();
        });
        
        Process::signal(SIGUSR2, function($signo) {
            echo "收到 SIGUSR2 信号，输出服务器状态...\n";
            $this->printServerStatus();
        });
    }
    
    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('shutdown', [$this, 'onShutdown']);
        $this->server->on('receive', [$this, 'onReceive']);
    }
    
    public function onStart($server) {
        echo "=== Master 进程启动 ===\n";
        echo "Master PID: {$server->master_pid}\n";
        echo "Manager PID: {$server->manager_pid}\n";
        
        // 保存 PID 到文件
        file_put_contents('/tmp/swoole_master.pid', $server->master_pid);
        
        echo "信号处理:\n";
        echo "- SIGTERM: 优雅关闭服务器\n";
        echo "- SIGUSR1: 重新加载 Worker 进程\n";
        echo "- SIGUSR2: 输出服务器状态\n\n";
        
        echo "测试命令:\n";
        echo "kill -USR1 {$server->master_pid}  # 重载 Worker\n";
        echo "kill -USR2 {$server->master_pid}  # 查看状态\n";
        echo "kill -TERM {$server->master_pid}  # 关闭服务器\n\n";
    }
    
    public function onShutdown($server) {
        echo "=== Master 进程关闭 ===\n";
        
        // 清理 PID 文件
        if (file_exists('/tmp/swoole_master.pid')) {
            unlink('/tmp/swoole_master.pid');
        }
    }
    
    public function onReceive($server, $fd, $reactorId, $data) {
        $message = trim($data);
        
        switch ($message) {
            case 'reload':
                $this->reloadWorkers();
                $server->send($fd, "Worker 进程重载完成\n");
                break;
            case 'status':
                $status = $this->getServerStatus();
                $server->send($fd, json_encode($status, JSON_PRETTY_PRINT) . "\n");
                break;
            case 'shutdown':
                $server->send($fd, "服务器即将关闭\n");
                $server->shutdown();
                break;
            default:
                $server->send($fd, "Echo: {$message}\n");
                break;
        }
    }
    
    private function reloadWorkers() {
        echo "重新加载 Worker 进程...\n";
        $this->server->reload();
    }
    
    private function printServerStatus() {
        $status = $this->getServerStatus();
        echo "=== 服务器状态 ===\n";
        foreach ($status as $key => $value) {
            echo "{$key}: {$value}\n";
        }
        echo "==================\n";
    }
    
    private function getServerStatus() {
        $stats = $this->server->stats();
        
        return [
            'start_time' => date('Y-m-d H:i:s', $stats['start_time']),
            'connection_num' => $stats['connection_num'],
            'accept_count' => $stats['accept_count'],
            'close_count' => $stats['close_count'],
            'worker_num' => $this->server->setting['worker_num'],
            'task_worker_num' => $this->server->setting['task_worker_num'] ?? 0,
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
        ];
    }
    
    public function start() {
        $this->server->start();
    }
}

$demo = new MasterProcessDemo();
$demo->start();
?>
```

### 15.2.2 Manager 进程

Manager 进程负责管理所有的 Worker 进程和 Task Worker 进程：

```php
<?php
// manager_process.php

use Swoole\Server;
use Swoole\Timer;

class ManagerProcessDemo {
    private $server;
    private $workerStats = [];
    
    public function __construct() {
        $this->server = new Server('0.0.0.0', 9502);
        $this->setupServer();
        $this->setupEvents();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 3,
            'task_worker_num' => 2,
            'max_request' => 1000,  // Worker 进程最大请求数
            'reload_async' => true, // 异步重启
            'daemonize' => false,
        ]);
    }
    
    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('managerStart', [$this, 'onManagerStart']);
        $this->server->on('managerStop', [$this, 'onManagerStop']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('workerStop', [$this, 'onWorkerStop']);
        $this->server->on('workerError', [$this, 'onWorkerError']);
        $this->server->on('workerExit', [$this, 'onWorkerExit']);
        $this->server->on('receive', [$this, 'onReceive']);
        $this->server->on('task', [$this, 'onTask']);
    }
    
    public function onStart($server) {
        echo "服务器启动，Master PID: {$server->master_pid}\n";
    }
    
    public function onManagerStart($server) {
        echo "=== Manager 进程启动 ===\n";
        echo "Manager PID: " . getmypid() . "\n";
        
        // 设置进程标题
        cli_set_process_title('swoole-manager');
        
        echo "Manager 进程职责:\n";
        echo "1. 创建和管理 Worker 进程\n";
        echo "2. 监控 Worker 进程状态\n";
        echo "3. 重启异常退出的 Worker\n";
        echo "4. 处理进程重载信号\n\n";
        
        // 初始化 Worker 统计信息
        for ($i = 0; $i < $server->setting['worker_num'] + ($server->setting['task_worker_num'] ?? 0); $i++) {
            $this->workerStats[$i] = [
                'start_time' => time(),
                'restart_count' => 0,
                'last_restart' => null,
                'status' => 'starting'
            ];
        }
    }
    
    public function onManagerStop($server) {
        echo "=== Manager 进程停止 ===\n";
        echo "所有 Worker 进程已停止\n";
    }
    
    public function onWorkerStart($server, $workerId) {
        $isTaskWorker = $workerId >= $server->setting['worker_num'];
        $processType = $isTaskWorker ? 'Task Worker' : 'Worker';
        
        echo "[Manager] {$processType} {$workerId} 启动，PID: " . getmypid() . "\n";
        
        // 更新统计信息
        $this->workerStats[$workerId]['status'] = 'running';
        $this->workerStats[$workerId]['start_time'] = time();
        
        if ($isTaskWorker) {
            cli_set_process_title("swoole-task-{$workerId}");
        } else {
            cli_set_process_title("swoole-worker-{$workerId}");
            
            // 在第一个 Worker 中启动监控定时器
            if ($workerId === 0) {
                Timer::tick(10000, function() use ($server) {
                    $this->monitorWorkers($server);
                });
            }
        }
    }
    
    public function onWorkerStop($server, $workerId) {
        echo "[Manager] Worker {$workerId} 正常停止\n";
        $this->workerStats[$workerId]['status'] = 'stopped';
    }
    
    public function onWorkerError($server, $workerId, $workerPid, $exitCode, $signal) {
        echo "[Manager] Worker {$workerId} 异常退出:\n";
        echo "  PID: {$workerPid}\n";
        echo "  Exit Code: {$exitCode}\n";
        echo "  Signal: {$signal}\n";
        
        // 更新统计信息
        $this->workerStats[$workerId]['restart_count']++;
        $this->workerStats[$workerId]['last_restart'] = time();
        $this->workerStats[$workerId]['status'] = 'error';
        
        // 记录错误日志
        $this->logWorkerError($workerId, $workerPid, $exitCode, $signal);
    }
    
    public function onWorkerExit($server, $workerId) {
        echo "[Manager] Worker {$workerId} 退出\n";
        $this->workerStats[$workerId]['status'] = 'exited';
    }
    
    public function onReceive($server, $fd, $reactorId, $data) {
        $message = trim($data);
        
        if ($message === 'worker_stats') {
            $stats = $this->getWorkerStats();
            $server->send($fd, json_encode($stats, JSON_PRETTY_PRINT) . "\n");
        } elseif ($message === 'crash_worker') {
            // 模拟 Worker 崩溃
            exit(1);
        } else {
            $workerId = $server->getWorkerId();
            $server->send($fd, "[Worker {$workerId}] Echo: {$message}\n");
        }
    }
    
    public function onTask($server, $taskId, $reactorId, $data) {
        $workerId = $server->getWorkerId();
        echo "[Task Worker {$workerId}] 处理任务 {$taskId}\n";
        
        // 模拟任务处理
        sleep(1);
        
        return "Task {$taskId} completed by Worker {$workerId}";
    }
    
    private function monitorWorkers($server) {
        echo "\n=== Worker 进程监控 ===\n";
        
        foreach ($this->workerStats as $workerId => $stats) {
            $uptime = time() - $stats['start_time'];
            $isTaskWorker = $workerId >= $server->setting['worker_num'];
            $type = $isTaskWorker ? 'Task' : 'Worker';
            
            echo "{$type} {$workerId}: ";
            echo "状态={$stats['status']}, ";
            echo "运行时间={$uptime}s, ";
            echo "重启次数={$stats['restart_count']}";
            
            if ($stats['last_restart']) {
                $lastRestart = time() - $stats['last_restart'];
                echo ", 上次重启={$lastRestart}s前";
            }
            
            echo "\n";
        }
        
        echo "========================\n\n";
    }
    
    private function logWorkerError($workerId, $workerPid, $exitCode, $signal) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'worker_id' => $workerId,
            'worker_pid' => $workerPid,
            'exit_code' => $exitCode,
            'signal' => $signal,
            'restart_count' => $this->workerStats[$workerId]['restart_count']
        ];
        
        $logFile = '/tmp/swoole_worker_errors.log';
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND);
    }
    
    private function getWorkerStats() {
        $stats = [];
        
        foreach ($this->workerStats as $workerId => $workerStat) {
            $isTaskWorker = $workerId >= $this->server->setting['worker_num'];
            
            $stats[] = [
                'worker_id' => $workerId,
                'type' => $isTaskWorker ? 'task_worker' : 'worker',
                'status' => $workerStat['status'],
                'uptime' => time() - $workerStat['start_time'],
                'restart_count' => $workerStat['restart_count'],
                'last_restart' => $workerStat['last_restart']
            ];
        }
        
        return $stats;
    }
    
    public function start() {
        echo "启动 Manager 进程演示...\n\n";
        $this->server->start();
    }
}

$demo = new ManagerProcessDemo();
$demo->start();
?>
```

### 15.2.3 Worker 进程

Worker 进程是处理业务逻辑的核心进程：

```php
<?php
// worker_process.php

use Swoole\Server;
use Swoole\Timer;
use Swoole\Coroutine;

class WorkerProcessDemo {
    private $server;
    private $requestCount = 0;
    private $connections = [];

    public function __construct() {
        $this->server = new Server('0.0.0.0', 9503);
        $this->setupServer();
        $this->setupEvents();
    }

    private function setupServer() {
        $this->server->set([
            'worker_num' => 4,
            'task_worker_num' => 2,
            'max_request' => 100,    // 每个 Worker 最多处理 100 个请求后重启
            'enable_coroutine' => true,
            'hook_flags' => SWOOLE_HOOK_ALL,
        ]);
    }

    private function setupEvents() {
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('workerStop', [$this, 'onWorkerStop']);
        $this->server->on('connect', [$this, 'onConnect']);
        $this->server->on('receive', [$this, 'onReceive']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('task', [$this, 'onTask']);
        $this->server->on('finish', [$this, 'onFinish']);
    }

    public function onWorkerStart($server, $workerId) {
        $isTaskWorker = $workerId >= $server->setting['worker_num'];

        if ($isTaskWorker) {
            echo "[Task Worker {$workerId}] 启动，PID: " . getmypid() . "\n";
            cli_set_process_title("swoole-task-{$workerId}");
            return;
        }

        echo "[Worker {$workerId}] 启动，PID: " . getmypid() . "\n";
        cli_set_process_title("swoole-worker-{$workerId}");

        // 重置计数器
        $this->requestCount = 0;
        $this->connections = [];

        echo "Worker {$workerId} 职责:\n";
        echo "- 处理客户端连接和请求\n";
        echo "- 执行业务逻辑\n";
        echo "- 管理连接状态\n";
        echo "- 投递异步任务\n\n";

        // 设置定时器（只在第一个 Worker 中）
        if ($workerId === 0) {
            Timer::tick(5000, function() use ($workerId) {
                $this->reportWorkerStatus($workerId);
            });
        }

        // 设置内存监控定时器
        Timer::tick(10000, function() use ($workerId) {
            $this->monitorMemory($workerId);
        });
    }

    public function onWorkerStop($server, $workerId) {
        echo "[Worker {$workerId}] 停止，处理了 {$this->requestCount} 个请求\n";
    }

    public function onConnect($server, $fd, $reactorId) {
        $workerId = $server->getWorkerId();
        $clientInfo = $server->getClientInfo($fd);

        $this->connections[$fd] = [
            'connect_time' => time(),
            'ip' => $clientInfo['remote_ip'],
            'port' => $clientInfo['remote_port'],
            'request_count' => 0
        ];

        echo "[Worker {$workerId}] 新连接: FD={$fd}, IP={$clientInfo['remote_ip']}\n";

        // 发送欢迎消息
        $welcome = "欢迎连接到 Worker {$workerId}！\n";
        $welcome .= "可用命令: info, stats, task, coroutine, memory, quit\n";
        $server->send($fd, $welcome);
    }

    public function onReceive($server, $fd, $reactorId, $data) {
        $workerId = $server->getWorkerId();
        $this->requestCount++;

        if (isset($this->connections[$fd])) {
            $this->connections[$fd]['request_count']++;
        }

        $message = trim($data);
        echo "[Worker {$workerId}] 收到请求: {$message} (第 {$this->requestCount} 个请求)\n";

        switch ($message) {
            case 'info':
                $this->handleInfo($server, $fd);
                break;
            case 'stats':
                $this->handleStats($server, $fd);
                break;
            case 'task':
                $this->handleTask($server, $fd);
                break;
            case 'coroutine':
                $this->handleCoroutine($server, $fd);
                break;
            case 'memory':
                $this->handleMemory($server, $fd);
                break;
            case 'quit':
                $server->close($fd);
                break;
            default:
                $server->send($fd, "[Worker {$workerId}] Echo: {$message}\n");
                break;
        }
    }

    public function onClose($server, $fd, $reactorId) {
        $workerId = $server->getWorkerId();

        if (isset($this->connections[$fd])) {
            $conn = $this->connections[$fd];
            $duration = time() - $conn['connect_time'];
            echo "[Worker {$workerId}] 连接关闭: FD={$fd}, 持续时间={$duration}s, 请求数={$conn['request_count']}\n";
            unset($this->connections[$fd]);
        }
    }

    public function onTask($server, $taskId, $reactorId, $data) {
        $workerId = $server->getWorkerId();
        echo "[Task Worker {$workerId}] 处理任务: {$taskId}\n";

        // 解析任务数据
        $taskData = json_decode($data, true);

        switch ($taskData['type']) {
            case 'email':
                return $this->sendEmail($taskData['data']);
            case 'log':
                return $this->writeLog($taskData['data']);
            case 'calculation':
                return $this->performCalculation($taskData['data']);
            default:
                return ['error' => 'Unknown task type'];
        }
    }

    public function onFinish($server, $taskId, $data) {
        $workerId = $server->getWorkerId();
        echo "[Worker {$workerId}] 任务完成: {$taskId}, 结果: " . json_encode($data) . "\n";
    }

    private function handleInfo($server, $fd) {
        $workerId = $server->getWorkerId();

        $info = [
            'worker_id' => $workerId,
            'process_pid' => getmypid(),
            'request_count' => $this->requestCount,
            'connection_count' => count($this->connections),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'coroutine_count' => Coroutine::stats()['coroutine_num'] ?? 0,
        ];

        $server->send($fd, json_encode($info, JSON_PRETTY_PRINT) . "\n");
    }

    private function handleStats($server, $fd) {
        $stats = $server->stats();
        $server->send($fd, json_encode($stats, JSON_PRETTY_PRINT) . "\n");
    }

    private function handleTask($server, $fd) {
        $taskData = [
            'type' => 'calculation',
            'data' => ['a' => rand(1, 100), 'b' => rand(1, 100)]
        ];

        $taskId = $server->task(json_encode($taskData));
        $server->send($fd, "任务已投递，Task ID: {$taskId}\n");
    }

    private function handleCoroutine($server, $fd) {
        // 启动协程演示
        Coroutine::create(function() use ($server, $fd) {
            $workerId = $server->getWorkerId();

            for ($i = 1; $i <= 3; $i++) {
                $server->send($fd, "[Worker {$workerId}] 协程步骤 {$i}\n");
                Coroutine::sleep(1); // 协程睡眠，不阻塞其他请求
            }

            $server->send($fd, "[Worker {$workerId}] 协程执行完成\n");
        });
    }

    private function handleMemory($server, $fd) {
        $memory = [
            'current_usage' => memory_get_usage(true),
            'peak_usage' => memory_get_peak_usage(true),
            'current_usage_real' => memory_get_usage(false),
            'peak_usage_real' => memory_get_peak_usage(false),
        ];

        foreach ($memory as $key => $value) {
            $memory[$key] = round($value / 1024 / 1024, 2) . ' MB';
        }

        $server->send($fd, json_encode($memory, JSON_PRETTY_PRINT) . "\n");
    }

    private function sendEmail($data) {
        // 模拟发送邮件
        sleep(2);
        return [
            'type' => 'email',
            'status' => 'sent',
            'to' => $data['to'],
            'subject' => $data['subject'],
            'sent_at' => date('Y-m-d H:i:s')
        ];
    }

    private function writeLog($data) {
        // 模拟写日志
        $logFile = '/tmp/swoole_task.log';
        $logEntry = date('Y-m-d H:i:s') . ' - ' . $data['message'] . "\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND);

        return [
            'type' => 'log',
            'status' => 'written',
            'file' => $logFile,
            'message' => $data['message']
        ];
    }

    private function performCalculation($data) {
        // 模拟复杂计算
        $result = 0;
        for ($i = 0; $i < 1000000; $i++) {
            $result += $data['a'] * $data['b'];
        }

        return [
            'type' => 'calculation',
            'input' => $data,
            'result' => $result,
            'iterations' => 1000000
        ];
    }

    private function reportWorkerStatus($workerId) {
        echo "\n=== Worker {$workerId} 状态报告 ===\n";
        echo "请求数: {$this->requestCount}\n";
        echo "连接数: " . count($this->connections) . "\n";
        echo "内存使用: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB\n";
        echo "协程数: " . (Coroutine::stats()['coroutine_num'] ?? 0) . "\n";
        echo "==============================\n\n";
    }

    private function monitorMemory($workerId) {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = 128 * 1024 * 1024; // 128MB

        if ($memoryUsage > $memoryLimit) {
            echo "[Worker {$workerId}] 警告: 内存使用超过限制 (" .
                 round($memoryUsage / 1024 / 1024, 2) . " MB)\n";
        }
    }

    public function start() {
        echo "启动 Worker 进程演示...\n\n";
        $this->server->start();
    }
}

$demo = new WorkerProcessDemo();
$demo->start();
?>
```

## 15.3 进程配置优化

### 15.3.1 进程数量配置

```php
<?php
// process_configuration.php

use Swoole\Server;

class ProcessConfiguration {
    private $server;

    public function __construct() {
        $this->server = new Server('0.0.0.0', 9504);
        $this->setupOptimalConfiguration();
        $this->setupEvents();
    }

    private function setupOptimalConfiguration() {
        // 获取系统信息
        $cpuCount = swoole_cpu_num();
        $memoryTotal = $this->getSystemMemory();

        echo "=== 系统信息 ===\n";
        echo "CPU 核心数: {$cpuCount}\n";
        echo "系统内存: " . round($memoryTotal / 1024 / 1024 / 1024, 2) . " GB\n\n";

        // 计算最优配置
        $config = $this->calculateOptimalConfig($cpuCount, $memoryTotal);

        echo "=== 推荐配置 ===\n";
        foreach ($config as $key => $value) {
            echo "{$key}: {$value}\n";
        }
        echo "\n";

        $this->server->set($config);
    }

    private function calculateOptimalConfig($cpuCount, $memoryTotal) {
        // Worker 进程数配置原则：
        // 1. CPU 密集型：worker_num = CPU 核心数
        // 2. I/O 密集型：worker_num = CPU 核心数 * 2
        // 3. 混合型：worker_num = CPU 核心数 * 1.5

        $workerNum = $cpuCount * 2; // 假设是 I/O 密集型应用

        // Task Worker 进程数：通常为 Worker 进程数的 1/2 到 1/4
        $taskWorkerNum = max(1, intval($workerNum / 2));

        // Reactor 线程数：通常等于 CPU 核心数
        $reactorNum = $cpuCount;

        // 最大连接数：根据内存计算
        // 每个连接大约占用 220KB 内存
        $maxConn = min(65536, intval($memoryTotal * 0.8 / (220 * 1024)));

        return [
            'worker_num' => $workerNum,
            'task_worker_num' => $taskWorkerNum,
            'reactor_num' => $reactorNum,
            'max_conn' => $maxConn,
            'max_request' => 10000,
            'dispatch_mode' => 2,
            'open_tcp_nodelay' => true,
            'heartbeat_check_interval' => 30,
            'heartbeat_idle_time' => 60,
            'buffer_output_size' => 2 * 1024 * 1024,
            'socket_buffer_size' => 128 * 1024 * 1024,
            'enable_coroutine' => true,
            'hook_flags' => SWOOLE_HOOK_ALL,
        ];
    }

    private function getSystemMemory() {
        // 获取系统内存（字节）
        $meminfo = file_get_contents('/proc/meminfo');
        preg_match('/MemTotal:\s+(\d+)\s+kB/', $meminfo, $matches);
        return isset($matches[1]) ? $matches[1] * 1024 : 8 * 1024 * 1024 * 1024; // 默认 8GB
    }

    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('receive', [$this, 'onReceive']);
    }

    public function onStart($server) {
        echo "=== 服务器启动 ===\n";
        echo "配置已应用，服务器运行中...\n\n";

        $this->printConfigurationAdvice();
    }

    public function onWorkerStart($server, $workerId) {
        $isTaskWorker = $workerId >= $server->setting['worker_num'];
        $type = $isTaskWorker ? 'Task Worker' : 'Worker';
        echo "{$type} {$workerId} 启动\n";
    }

    public function onReceive($server, $fd, $reactorId, $data) {
        $message = trim($data);

        if ($message === 'config') {
            $config = $server->setting;
            $server->send($fd, json_encode($config, JSON_PRETTY_PRINT) . "\n");
        } else {
            $server->send($fd, "Echo: {$message}\n");
        }
    }

    private function printConfigurationAdvice() {
        echo "=== 配置建议 ===\n";
        echo "1. Worker 进程数配置:\n";
        echo "   - CPU 密集型: worker_num = CPU 核心数\n";
        echo "   - I/O 密集型: worker_num = CPU 核心数 * 2\n";
        echo "   - 混合型: worker_num = CPU 核心数 * 1.5\n\n";

        echo "2. Task Worker 进程数:\n";
        echo "   - 通常为 Worker 进程数的 1/4 到 1/2\n";
        echo "   - 根据异步任务的数量和复杂度调整\n\n";

        echo "3. 内存配置:\n";
        echo "   - 每个 Worker 进程约占用 8-32MB 内存\n";
        echo "   - 每个连接约占用 220KB 内存\n";
        echo "   - 预留足够的系统内存\n\n";

        echo "4. 性能调优:\n";
        echo "   - 启用协程提高并发性能\n";
        echo "   - 合理设置心跳检测\n";
        echo "   - 调整缓冲区大小\n";
        echo "   - 使用适当的分发模式\n\n";
    }

    public function start() {
        $this->server->start();
    }
}

$config = new ProcessConfiguration();
$config->start();
?>
```

## 本章练习

### 练习 1：进程监控系统
实现一个完整的进程监控系统：

```php
<?php
// process_monitor.php

class ProcessMonitor {
    // 实现功能：
    // 1. 实时监控所有进程状态
    // 2. 进程性能指标收集
    // 3. 异常进程检测和告警
    // 4. 进程重启策略
    // 5. 监控数据可视化
    // 6. 历史数据存储和分析
}

class ProcessHealthChecker {
    // 实现功能：
    // 1. 进程健康检查
    // 2. 内存泄漏检测
    // 3. CPU 使用率监控
    // 4. 响应时间监控
    // 5. 自动恢复机制
}
?>
```

### 练习 2：动态进程管理
实现动态进程数量调整：

```php
<?php
// dynamic_process_manager.php

class DynamicProcessManager {
    // 实现功能：
    // 1. 根据负载动态调整进程数
    // 2. 平滑扩容和缩容
    // 3. 负载预测算法
    // 4. 资源使用优化
    // 5. 配置热更新
    // 6. 性能基准测试
}

class LoadBalancer {
    // 实现功能：
    // 1. 智能负载分发
    // 2. 进程亲和性管理
    // 3. 请求路由优化
    // 4. 故障转移机制
}
?>
```

### 练习 3：进程间通信优化
优化进程间通信性能：

```php
<?php
// ipc_optimization.php

class IPCOptimizer {
    // 实现功能：
    // 1. 多种 IPC 方式对比
    // 2. 通信协议优化
    // 3. 数据序列化优化
    // 4. 批量数据传输
    // 5. 通信延迟监控
    // 6. 吞吐量优化
}

class MessageQueue {
    // 实现功能：
    // 1. 高性能消息队列
    // 2. 消息持久化
    // 3. 消息确认机制
    // 4. 死信队列处理
    // 5. 消息路由
}
?>
```

### 练习 4：进程故障恢复
实现进程故障恢复机制：

```php
<?php
// process_recovery.php

class ProcessRecovery {
    // 实现功能：
    // 1. 进程崩溃检测
    // 2. 自动重启机制
    // 3. 状态恢复
    // 4. 数据一致性保证
    // 5. 故障日志记录
    // 6. 恢复策略配置
}

class StateManager {
    // 实现功能：
    // 1. 进程状态持久化
    // 2. 状态同步机制
    // 3. 状态版本管理
    // 4. 状态回滚
    // 5. 分布式状态管理
}
?>
```

## 本章小结

本章深入介绍了 Swoole 的多进程架构，从基础概念到实际应用，涵盖了进程管理的各个方面。

**关键要点：**

- **进程架构**：Master、Manager、Worker、Task Worker 的职责分工
- **进程生命周期**：启动、运行、重启、停止的完整流程
- **进程配置**：根据系统资源和应用特点优化配置
- **进程监控**：实时监控进程状态和性能指标
- **故障处理**：异常检测、自动恢复、日志记录

**设计原则：**

1. **职责分离**：不同类型进程承担不同职责
2. **资源隔离**：进程间相互独立，避免相互影响
3. **故障隔离**：单个进程故障不影响整体服务
4. **动态管理**：支持进程的动态创建和销毁
5. **性能优化**：合理配置进程数量和资源分配

**最佳实践：**

1. **合理配置进程数量**：
   - Worker 进程数 = CPU 核心数 × 1-2
   - Task Worker 进程数 = Worker 进程数 ÷ 2-4
   - Reactor 线程数 = CPU 核心数

2. **内存管理**：
   - 设置合理的 max_request 避免内存泄漏
   - 监控进程内存使用情况
   - 及时释放不必要的资源

3. **进程监控**：
   - 实时监控进程状态
   - 记录性能指标
   - 设置告警机制

4. **故障处理**：
   - 实现进程健康检查
   - 配置自动重启策略
   - 记录详细的错误日志

**性能优化建议：**

1. 根据应用类型选择合适的进程数量
2. 启用协程提高并发处理能力
3. 合理设置缓冲区大小
4. 使用适当的任务分发模式
5. 定期监控和调整配置参数

**下一章预告：**
下一章我们将学习进程间通信，了解 Swoole 提供的各种 IPC 机制和最佳实践。
