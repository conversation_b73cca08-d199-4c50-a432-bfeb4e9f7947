# 第3章：基本概念与核心特性

## 学习目标
- 理解 Swoole 的核心概念和工作原理
- 掌握事件驱动编程模型
- 了解协程的基本概念和使用
- 理解进程模型和内存管理

## 3.1 核心概念

### 3.1.1 事件驱动 (Event-Driven)

事件驱动是 Swoole 的核心编程模型，程序的执行流程由事件来决定。

```php
<?php
// 事件驱动示例
use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 注册事件回调函数
$server->on("start", function ($server) {
    echo "服务器启动事件触发\n";
});

$server->on("request", function ($request, $response) {
    echo "请求事件触发\n";
    $response->end("Hello World");
});

$server->on("shutdown", function ($server) {
    echo "服务器关闭事件触发\n";
});

$server->start();
?>
```

**事件驱动的特点：**
- 非阻塞执行
- 高并发处理
- 资源利用率高
- 响应速度快

### 3.1.2 异步非阻塞 I/O

传统的同步阻塞 I/O 会让程序等待操作完成，而异步非阻塞 I/O 允许程序在等待期间处理其他任务。

```php
<?php
// 同步阻塞示例
$start = microtime(true);
$data1 = file_get_contents('http://httpbin.org/delay/1');
$data2 = file_get_contents('http://httpbin.org/delay/1');
$end = microtime(true);
echo "同步执行耗时: " . ($end - $start) . " 秒\n"; // 约 2 秒

// 异步非阻塞示例
use function Swoole\Coroutine\run;
use Swoole\Coroutine\Http\Client;

run(function () {
    $start = microtime(true);
    
    // 并发执行
    go(function () use (&$data1) {
        $client = new Client('httpbin.org', 80);
        $client->get('/delay/1');
        $data1 = $client->body;
        $client->close();
    });
    
    go(function () use (&$data2) {
        $client = new Client('httpbin.org', 80);
        $client->get('/delay/1');
        $data2 = $client->body;
        $client->close();
    });
    
    $end = microtime(true);
    echo "异步执行耗时: " . ($end - $start) . " 秒\n"; // 约 1 秒
});
?>
```

### 3.1.3 协程 (Coroutine)

协程是 Swoole 4.0+ 的核心特性，它是一种用户态的轻量级线程。

```php
<?php
use function Swoole\Coroutine\run;
use Swoole\Coroutine;

run(function () {
    echo "主协程开始\n";
    
    // 创建子协程
    $cid = Coroutine::create(function () {
        echo "子协程 1 开始\n";
        Coroutine::sleep(1); // 协程睡眠，让出 CPU
        echo "子协程 1 结束\n";
    });
    
    echo "子协程 ID: {$cid}\n";
    
    // 创建另一个子协程
    Coroutine::create(function () {
        echo "子协程 2 开始\n";
        Coroutine::sleep(0.5);
        echo "子协程 2 结束\n";
    });
    
    echo "主协程结束\n";
});
?>
```

**协程的特点：**
- 轻量级：创建成本低
- 高并发：支持数万个协程
- 同步写法：代码易读易维护
- 自动调度：无需手动管理

## 3.2 进程模型

### 3.2.1 Master-Worker 模型

Swoole 采用多进程的 Master-Worker 模型：

```
Master 进程
├── Manager 进程
│   ├── Worker 进程 1
│   ├── Worker 进程 2
│   ├── Worker 进程 N
│   ├── Task 进程 1 (可选)
│   └── Task 进程 N (可选)
└── 其他辅助进程
```

```php
<?php
use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 配置进程数量
$server->set([
    'worker_num' => 4,      // Worker 进程数
    'task_worker_num' => 2, // Task 进程数
]);

$server->on("workerstart", function ($server, $worker_id) {
    if ($worker_id >= $server->setting['worker_num']) {
        echo "Task Worker {$worker_id} 启动\n";
    } else {
        echo "Worker {$worker_id} 启动\n";
    }
});

$server->on("request", function ($request, $response) {
    $response->end("Worker PID: " . getmypid());
});

$server->start();
?>
```

### 3.2.2 进程职责分工

**Master 进程：**
- 主进程，负责管理其他进程
- 监听端口，接受新连接
- 分发连接给 Worker 进程

**Manager 进程：**
- 管理 Worker 和 Task 进程
- 进程异常时自动重启
- 平滑重启功能

**Worker 进程：**
- 处理客户端请求
- 执行业务逻辑
- 可以投递任务给 Task 进程

**Task 进程：**
- 处理异步任务
- 执行耗时操作
- 不阻塞 Worker 进程

## 3.3 内存管理

### 3.3.1 共享内存

Swoole 提供了多种共享内存机制：

```php
<?php
use Swoole\Table;

// 创建内存表
$table = new Table(1024);
$table->column('name', Table::TYPE_STRING, 64);
$table->column('age', Table::TYPE_INT);
$table->column('score', Table::TYPE_FLOAT);
$table->create();

// 写入数据
$table->set('user1', ['name' => 'Alice', 'age' => 25, 'score' => 95.5]);
$table->set('user2', ['name' => 'Bob', 'age' => 30, 'score' => 88.0]);

// 读取数据
$user = $table->get('user1');
echo "用户: {$user['name']}, 年龄: {$user['age']}, 分数: {$user['score']}\n";

// 遍历数据
foreach ($table as $key => $value) {
    echo "Key: {$key}, Value: " . json_encode($value) . "\n";
}
?>
```

### 3.3.2 原子计数器

```php
<?php
use Swoole\Atomic;

$atomic = new Atomic(0);

// 增加
$atomic->add(10);
echo "当前值: " . $atomic->get() . "\n"; // 10

// 减少
$atomic->sub(3);
echo "当前值: " . $atomic->get() . "\n"; // 7

// 比较并交换
$old_value = $atomic->cmpset(7, 100);
echo "交换成功: " . ($old_value ? 'true' : 'false') . "\n";
echo "当前值: " . $atomic->get() . "\n"; // 100
?>
```

## 3.4 事件循环

### 3.4.1 EventLoop 机制

Swoole 基于 epoll/kqueue 实现高性能的事件循环：

```php
<?php
use Swoole\Event;

// 添加读事件
$fp = stream_socket_client("tcp://www.baidu.com:80", $errno, $errstr, 30);
fwrite($fp, "GET / HTTP/1.1\r\nHost: www.baidu.com\r\n\r\n");

Event::add($fp, function ($fp) {
    $data = fread($fp, 8192);
    echo "接收到数据: " . strlen($data) . " 字节\n";
    Event::del($fp);
    fclose($fp);
});

// 添加定时器
$timer_id = Event::addTimer(1000, function () {
    echo "定时器触发: " . date('Y-m-d H:i:s') . "\n";
});

// 5 秒后删除定时器
Event::addTimer(5000, function () use ($timer_id) {
    Event::delTimer($timer_id);
    echo "定时器已删除\n";
});

// 启动事件循环
Event::wait();
?>
```

### 3.4.2 信号处理

```php
<?php
use Swoole\Process;

// 注册信号处理器
Process::signal(SIGTERM, function ($signo) {
    echo "收到 SIGTERM 信号，程序即将退出\n";
    exit(0);
});

Process::signal(SIGUSR1, function ($signo) {
    echo "收到 SIGUSR1 信号，执行自定义操作\n";
});

echo "程序运行中，PID: " . getmypid() . "\n";
echo "发送信号测试: kill -USR1 " . getmypid() . "\n";

// 保持程序运行
while (true) {
    sleep(1);
}
?>
```

## 3.5 协程调度器

### 3.5.1 协程调度原理

协程调度器负责管理协程的创建、暂停、恢复和销毁：

```php
<?php
use function Swoole\Coroutine\run;
use Swoole\Coroutine;

run(function () {
    echo "=== 协程调度演示 ===\n";

    // 协程 1
    Coroutine::create(function () {
        echo "协程 1 开始执行\n";
        echo "协程 1 当前 ID: " . Coroutine::getCid() . "\n";

        // 主动让出 CPU
        Coroutine::sleep(0.1);
        echo "协程 1 恢复执行\n";

        // 再次让出 CPU
        Coroutine::yield();
        echo "协程 1 被手动恢复\n";
    });

    // 协程 2
    $cid2 = Coroutine::create(function () {
        echo "协程 2 开始执行\n";
        echo "协程 2 当前 ID: " . Coroutine::getCid() . "\n";

        // 恢复协程 1
        Coroutine::sleep(0.2);
        echo "协程 2 准备恢复其他协程\n";
    });

    echo "主协程创建了子协程\n";
    echo "当前协程总数: " . Coroutine::stats()['coroutine_num'] . "\n";
});
?>
```

### 3.5.2 协程上下文

每个协程都有独立的上下文环境：

```php
<?php
use function Swoole\Coroutine\run;
use Swoole\Coroutine;

run(function () {
    // 设置协程上下文
    Coroutine::getContext()['user_id'] = 'main_user';

    Coroutine::create(function () {
        // 子协程有独立的上下文
        $context = Coroutine::getContext();
        $context['user_id'] = 'child_user_1';
        $context['request_id'] = uniqid();

        echo "协程 1 用户ID: " . $context['user_id'] . "\n";
        echo "协程 1 请求ID: " . $context['request_id'] . "\n";

        Coroutine::sleep(0.1);

        // 上下文在协程恢复后仍然保持
        echo "协程 1 恢复后用户ID: " . Coroutine::getContext()['user_id'] . "\n";
    });

    Coroutine::create(function () {
        $context = Coroutine::getContext();
        $context['user_id'] = 'child_user_2';
        $context['request_id'] = uniqid();

        echo "协程 2 用户ID: " . $context['user_id'] . "\n";
        echo "协程 2 请求ID: " . $context['request_id'] . "\n";
    });

    echo "主协程用户ID: " . Coroutine::getContext()['user_id'] . "\n";
});
?>
```

## 3.6 错误处理

### 3.6.1 异常处理

```php
<?php
use function Swoole\Coroutine\run;
use Swoole\Coroutine;

run(function () {
    try {
        Coroutine::create(function () {
            echo "协程开始执行\n";

            // 模拟异常
            if (rand(1, 2) === 1) {
                throw new Exception("协程执行异常");
            }

            echo "协程正常结束\n";
        });
    } catch (Exception $e) {
        echo "捕获异常: " . $e->getMessage() . "\n";
    }

    echo "主协程继续执行\n";
});
?>
```

### 3.6.2 错误回调

```php
<?php
use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

// 设置错误处理
$server->on("workerError", function ($server, $worker_id, $worker_pid, $exit_code, $signal) {
    echo "Worker 进程异常: Worker ID={$worker_id}, PID={$worker_pid}, 退出码={$exit_code}, 信号={$signal}\n";
});

$server->on("request", function ($request, $response) {
    try {
        // 业务逻辑
        $response->end("Hello World");
    } catch (Throwable $e) {
        echo "请求处理异常: " . $e->getMessage() . "\n";
        $response->status(500);
        $response->end("Internal Server Error");
    }
});

$server->start();
?>
```

## 本章练习

### 练习 1：事件驱动编程
创建一个简单的事件驱动程序，实现以下功能：
1. 监听文件变化事件
2. 处理定时器事件
3. 处理信号事件

```php
<?php
// event_practice.php

use Swoole\Event;
use Swoole\Process;

// 练习代码在这里实现
?>
```

### 练习 2：协程并发处理
编写程序模拟并发处理多个任务：

```php
<?php
// coroutine_practice.php

use function Swoole\Coroutine\run;
use Swoole\Coroutine;

run(function () {
    $tasks = [
        'task1' => 1.0,  // 耗时 1 秒
        'task2' => 0.5,  // 耗时 0.5 秒
        'task3' => 1.5,  // 耗时 1.5 秒
        'task4' => 0.8,  // 耗时 0.8 秒
    ];

    $start = microtime(true);

    // 实现并发执行所有任务
    // 计算总耗时并与串行执行对比

    $end = microtime(true);
    echo "并发执行总耗时: " . ($end - $start) . " 秒\n";
});
?>
```

### 练习 3：内存表应用
使用 Swoole Table 实现一个简单的缓存系统：

```php
<?php
// table_practice.php

use Swoole\Table;

// 创建缓存表
$cache = new Table(1024);
$cache->column('value', Table::TYPE_STRING, 1024);
$cache->column('expire', Table::TYPE_INT);
$cache->create();

// 实现缓存的基本操作：
// 1. set($key, $value, $ttl) - 设置缓存
// 2. get($key) - 获取缓存
// 3. delete($key) - 删除缓存
// 4. clear() - 清空缓存

class SimpleCache {
    private $table;

    public function __construct(Table $table) {
        $this->table = $table;
    }

    // 实现缓存方法
}
?>
```

### 练习 4：进程模型理解
创建一个多进程程序，观察进程间的关系：

```php
<?php
// process_practice.php

use Swoole\Http\Server;

$server = new Server("127.0.0.1", 9501);

$server->set([
    'worker_num' => 2,
    'task_worker_num' => 1,
]);

// 在不同的事件回调中输出进程信息
// 观察 Master、Manager、Worker、Task 进程的关系

$server->start();
?>
```

## 本章小结

本章深入介绍了 Swoole 的核心概念和基本特性，包括事件驱动编程、协程机制、进程模型、内存管理等。这些概念是理解和使用 Swoole 的基础，需要通过实践来加深理解。

**关键要点：**
- 事件驱动是 Swoole 的核心编程模型
- 协程提供了同步写法的异步编程体验
- Master-Worker 进程模型保证了高性能和稳定性
- 共享内存机制支持进程间数据共享
- 理解这些概念对后续学习至关重要

**下一章预告：**
下一章我们将编写第一个完整的 Swoole 程序，将理论知识应用到实践中。
