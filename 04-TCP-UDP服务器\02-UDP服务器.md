# 第14章：UDP 服务器开发

## 学习目标
- 理解 UDP 协议的特点和应用场景
- 掌握 Swoole UDP 服务器的创建和配置
- 学会处理 UDP 数据包的发送和接收
- 了解 UDP 服务器的性能优化和可靠性保证

## 14.1 UDP 协议基础

### 14.1.1 UDP 协议特点

UDP (User Datagram Protocol) 是一种无连接的传输层协议。它具有以下特点：

- **无连接**：发送数据前不需要建立连接
- **不可靠**：不保证数据包的到达和顺序
- **低开销**：协议头部简单，开销小
- **快速**：没有连接建立和维护的开销
- **支持广播和组播**：可以一对多通信

```php
<?php
// basic_udp_server.php

use Swoole\Server;

class BasicUDPServer {
    private $server;
    private $stats = [
        'packets_received' => 0,
        'packets_sent' => 0,
        'bytes_received' => 0,
        'bytes_sent' => 0,
        'start_time' => 0,
        'clients' => []
    ];
    
    public function __construct($host = '0.0.0.0', $port = 9501) {
        $this->server = new Server($host, $port, SWOOLE_PROCESS, SWOOLE_SOCK_UDP);
        $this->setupServer();
        $this->setupEvents();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 2,
            'max_conn' => 10000,
            'dispatch_mode' => 2,
            'open_cpu_affinity' => true,
            'daemonize' => false,
        ]);
    }
    
    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('packet', [$this, 'onPacket']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
    }
    
    public function onStart($server) {
        $this->stats['start_time'] = time();
        echo "UDP 服务器启动成功\n";
        echo "监听地址: {$server->host}:{$server->port}\n";
        echo "Master PID: {$server->master_pid}\n";
    }
    
    public function onWorkerStart($server, $workerId) {
        echo "Worker #{$workerId} 启动，PID: " . getmypid() . "\n";
    }
    
    public function onPacket($server, $data, $clientInfo) {
        $this->stats['packets_received']++;
        $this->stats['bytes_received'] += strlen($data);
        
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        // 记录客户端信息
        if (!isset($this->stats['clients'][$clientKey])) {
            $this->stats['clients'][$clientKey] = [
                'first_seen' => time(),
                'last_seen' => time(),
                'packet_count' => 0,
                'bytes_received' => 0
            ];
        }
        
        $this->stats['clients'][$clientKey]['last_seen'] = time();
        $this->stats['clients'][$clientKey]['packet_count']++;
        $this->stats['clients'][$clientKey]['bytes_received'] += strlen($data);
        
        echo "收到数据包: 来自 {$clientKey}, 长度 " . strlen($data) . " 字节\n";
        echo "内容: {$data}\n";
        
        // 处理数据包
        $this->handlePacket($server, $data, $clientInfo);
    }
    
    private function handlePacket($server, $data, $clientInfo) {
        $data = trim($data);
        
        // 解析命令
        $parts = explode(' ', $data, 2);
        $command = strtoupper($parts[0]);
        $args = $parts[1] ?? '';
        
        switch ($command) {
            case 'PING':
                $this->handlePing($server, $clientInfo);
                break;
            case 'ECHO':
                $this->handleEcho($server, $clientInfo, $args);
                break;
            case 'TIME':
                $this->handleTime($server, $clientInfo);
                break;
            case 'STATS':
                $this->handleStats($server, $clientInfo);
                break;
            case 'BROADCAST':
                $this->handleBroadcast($server, $clientInfo, $args);
                break;
            case 'CALC':
                $this->handleCalculation($server, $clientInfo, $args);
                break;
            default:
                $this->sendResponse($server, $clientInfo, "未知命令: {$command}");
                break;
        }
    }
    
    private function handlePing($server, $clientInfo) {
        $this->sendResponse($server, $clientInfo, "PONG");
    }
    
    private function handleEcho($server, $clientInfo, $message) {
        $this->sendResponse($server, $clientInfo, "ECHO: {$message}");
    }
    
    private function handleTime($server, $clientInfo) {
        $time = date('Y-m-d H:i:s');
        $this->sendResponse($server, $clientInfo, "当前时间: {$time}");
    }
    
    private function handleStats($server, $clientInfo) {
        $uptime = time() - $this->stats['start_time'];
        $clientCount = count($this->stats['clients']);
        
        $stats = [
            "服务器运行时间: {$uptime} 秒",
            "接收数据包: {$this->stats['packets_received']}",
            "发送数据包: {$this->stats['packets_sent']}",
            "接收字节数: {$this->stats['bytes_received']}",
            "发送字节数: {$this->stats['bytes_sent']}",
            "客户端数量: {$clientCount}",
            "内存使用: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB"
        ];
        
        $this->sendResponse($server, $clientInfo, implode("\n", $stats));
    }
    
    private function handleBroadcast($server, $clientInfo, $message) {
        if (empty($message)) {
            $this->sendResponse($server, $clientInfo, "广播消息不能为空");
            return;
        }
        
        $senderKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        $broadcastMsg = "广播消息 [来自 {$senderKey}]: {$message}";
        
        $count = 0;
        foreach ($this->stats['clients'] as $clientKey => $client) {
            if ($clientKey !== $senderKey) {
                list($ip, $port) = explode(':', $clientKey);
                $this->sendResponse($server, ['address' => $ip, 'port' => $port], $broadcastMsg);
                $count++;
            }
        }
        
        $this->sendResponse($server, $clientInfo, "广播完成，发送给 {$count} 个客户端");
    }
    
    private function handleCalculation($server, $clientInfo, $expression) {
        if (empty($expression)) {
            $this->sendResponse($server, $clientInfo, "请提供计算表达式");
            return;
        }
        
        // 简单的计算器（仅支持基本运算）
        $expression = preg_replace('/[^0-9+\-*\/\(\)\.\s]/', '', $expression);
        
        try {
            $result = eval("return {$expression};");
            $this->sendResponse($server, $clientInfo, "计算结果: {$expression} = {$result}");
        } catch (Exception $e) {
            $this->sendResponse($server, $clientInfo, "计算错误: 无效的表达式");
        }
    }
    
    private function sendResponse($server, $clientInfo, $message) {
        $result = $server->sendto($clientInfo['address'], $clientInfo['port'], $message);
        
        if ($result) {
            $this->stats['packets_sent']++;
            $this->stats['bytes_sent'] += strlen($message);
        }
        
        return $result;
    }
    
    public function start() {
        $this->server->start();
    }
}

$server = new BasicUDPServer('0.0.0.0', 9501);
$server->start();
?>
```

### 14.1.2 UDP 客户端测试工具

```php
<?php
// udp_client.php

class UDPClient {
    private $socket;
    private $serverHost;
    private $serverPort;
    
    public function __construct($host = '127.0.0.1', $port = 9501) {
        $this->serverHost = $host;
        $this->serverPort = $port;
        $this->createSocket();
    }
    
    private function createSocket() {
        $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        
        if (!$this->socket) {
            throw new Exception("无法创建 UDP socket: " . socket_strerror(socket_last_error()));
        }
        
        // 设置超时
        socket_set_option($this->socket, SOL_SOCKET, SO_RCVTIMEO, ['sec' => 5, 'usec' => 0]);
    }
    
    public function send($message) {
        $result = socket_sendto($this->socket, $message, strlen($message), 0, $this->serverHost, $this->serverPort);
        
        if ($result === false) {
            throw new Exception("发送失败: " . socket_strerror(socket_last_error($this->socket)));
        }
        
        return $result;
    }
    
    public function receive() {
        $buffer = '';
        $from = '';
        $port = 0;
        
        $result = socket_recvfrom($this->socket, $buffer, 65536, 0, $from, $port);
        
        if ($result === false) {
            $error = socket_last_error($this->socket);
            if ($error === SOCKET_EAGAIN || $error === SOCKET_EWOULDBLOCK) {
                throw new Exception("接收超时");
            } else {
                throw new Exception("接收失败: " . socket_strerror($error));
            }
        }
        
        return [
            'data' => $buffer,
            'from' => $from,
            'port' => $port,
            'length' => $result
        ];
    }
    
    public function sendAndReceive($message) {
        $this->send($message);
        return $this->receive();
    }
    
    public function close() {
        if ($this->socket) {
            socket_close($this->socket);
            $this->socket = null;
        }
    }
    
    public function __destruct() {
        $this->close();
    }
}

// 交互式客户端
function runInteractiveClient() {
    $client = new UDPClient();
    
    echo "UDP 客户端已启动\n";
    echo "可用命令:\n";
    echo "  PING - 测试连接\n";
    echo "  ECHO <message> - 回显消息\n";
    echo "  TIME - 获取服务器时间\n";
    echo "  STATS - 获取服务器统计\n";
    echo "  BROADCAST <message> - 广播消息\n";
    echo "  CALC <expression> - 计算表达式\n";
    echo "  EXIT - 退出\n\n";
    
    while (true) {
        echo "请输入命令: ";
        $input = trim(fgets(STDIN));
        
        if (empty($input)) {
            continue;
        }
        
        if (strtoupper($input) === 'EXIT') {
            break;
        }
        
        try {
            echo "发送: {$input}\n";
            $response = $client->sendAndReceive($input);
            echo "响应: {$response['data']}\n";
            echo "来自: {$response['from']}:{$response['port']}\n\n";
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n\n";
        }
    }
    
    $client->close();
    echo "客户端已关闭\n";
}

// 批量测试
function runBatchTest() {
    $commands = [
        'PING',
        'TIME',
        'ECHO Hello UDP Server',
        'CALC 2 + 3 * 4',
        'STATS',
        'BROADCAST Hello everyone!'
    ];
    
    for ($i = 1; $i <= 3; $i++) {
        echo "=== 客户端 {$i} 测试 ===\n";
        
        $client = new UDPClient();
        
        foreach ($commands as $command) {
            try {
                echo "发送: {$command}\n";
                $response = $client->sendAndReceive($command);
                echo "响应: {$response['data']}\n\n";
                
                usleep(500000); // 0.5秒延迟
                
            } catch (Exception $e) {
                echo "错误: " . $e->getMessage() . "\n\n";
            }
        }
        
        $client->close();
        echo "\n";
    }
}

// 性能测试
function runPerformanceTest() {
    echo "=== UDP 性能测试 ===\n";
    
    $client = new UDPClient();
    $testCount = 1000;
    $message = "PING";
    
    $startTime = microtime(true);
    $successCount = 0;
    
    for ($i = 0; $i < $testCount; $i++) {
        try {
            $client->sendAndReceive($message);
            $successCount++;
        } catch (Exception $e) {
            // 忽略错误，继续测试
        }
        
        if (($i + 1) % 100 === 0) {
            echo "已完成 " . ($i + 1) . " 次测试\n";
        }
    }
    
    $endTime = microtime(true);
    $duration = $endTime - $startTime;
    $rps = $successCount / $duration;
    
    echo "\n性能测试结果:\n";
    echo "总测试次数: {$testCount}\n";
    echo "成功次数: {$successCount}\n";
    echo "失败次数: " . ($testCount - $successCount) . "\n";
    echo "总耗时: " . round($duration, 2) . " 秒\n";
    echo "平均 RPS: " . round($rps, 2) . "\n";
    echo "平均延迟: " . round(($duration / $successCount) * 1000, 2) . " ms\n";
    
    $client->close();
}

// 根据命令行参数选择运行模式
if ($argc > 1) {
    switch ($argv[1]) {
        case 'batch':
            runBatchTest();
            break;
        case 'perf':
            runPerformanceTest();
            break;
        default:
            echo "未知参数: {$argv[1]}\n";
            echo "可用参数: batch, perf\n";
            break;
    }
} else {
    runInteractiveClient();
}
?>
```

## 14.2 高级 UDP 服务器

### 14.2.1 可靠 UDP 实现

```php
<?php
// reliable_udp_server.php

use Swoole\Server;
use Swoole\Timer;

class ReliableUDPServer {
    private $server;
    private $sessions = [];
    private $pendingAcks = [];
    private $sequenceNumbers = [];
    private $retransmitTimer;
    
    const PACKET_TYPE_DATA = 1;
    const PACKET_TYPE_ACK = 2;
    const PACKET_TYPE_HEARTBEAT = 3;
    const PACKET_TYPE_CONNECT = 4;
    const PACKET_TYPE_DISCONNECT = 5;
    
    const MAX_RETRIES = 3;
    const RETRANSMIT_TIMEOUT = 1000; // 1秒
    const SESSION_TIMEOUT = 30000; // 30秒
    
    public function __construct($host = '0.0.0.0', $port = 9502) {
        $this->server = new Server($host, $port, SWOOLE_PROCESS, SWOOLE_SOCK_UDP);
        $this->setupServer();
        $this->setupEvents();
        $this->setupTimers();
    }
    
    private function setupServer() {
        $this->server->set([
            'worker_num' => 2,
            'max_conn' => 10000,
            'dispatch_mode' => 2,
        ]);
    }
    
    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('packet', [$this, 'onPacket']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
    }
    
    private function setupTimers() {
        // 重传定时器
        $this->retransmitTimer = Timer::tick(self::RETRANSMIT_TIMEOUT, function() {
            $this->handleRetransmit();
        });
        
        // 会话清理定时器
        Timer::tick(10000, function() {
            $this->cleanupSessions();
        });
    }
    
    public function onStart($server) {
        echo "可靠 UDP 服务器启动成功，监听 {$server->host}:{$server->port}\n";
    }
    
    public function onWorkerStart($server, $workerId) {
        echo "Worker #{$workerId} 启动\n";
    }
    
    public function onPacket($server, $data, $clientInfo) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        // 解析数据包
        $packet = $this->parsePacket($data);
        if (!$packet) {
            echo "无效数据包来自 {$clientKey}\n";
            return;
        }
        
        echo "收到数据包: 类型={$packet['type']}, 序号={$packet['seq']}, 来自={$clientKey}\n";
        
        // 更新会话
        $this->updateSession($clientKey, $clientInfo);
        
        switch ($packet['type']) {
            case self::PACKET_TYPE_CONNECT:
                $this->handleConnect($server, $clientInfo, $packet);
                break;
            case self::PACKET_TYPE_DATA:
                $this->handleData($server, $clientInfo, $packet);
                break;
            case self::PACKET_TYPE_ACK:
                $this->handleAck($clientInfo, $packet);
                break;
            case self::PACKET_TYPE_HEARTBEAT:
                $this->handleHeartbeat($server, $clientInfo, $packet);
                break;
            case self::PACKET_TYPE_DISCONNECT:
                $this->handleDisconnect($clientInfo, $packet);
                break;
        }
    }
    
    private function parsePacket($data) {
        if (strlen($data) < 8) {
            return null;
        }
        
        $header = unpack('Ctype/Nseq/nchecksum/C', substr($data, 0, 8));
        $payload = substr($data, 8);
        
        // 简单的校验和验证
        $calculatedChecksum = crc32($payload) & 0xFFFF;
        if ($header['checksum'] !== $calculatedChecksum) {
            return null;
        }
        
        return [
            'type' => $header['type'],
            'seq' => $header['seq'],
            'checksum' => $header['checksum'],
            'payload' => $payload
        ];
    }
    
    private function createPacket($type, $seq, $payload = '') {
        $checksum = crc32($payload) & 0xFFFF;
        return pack('CNnC', $type, $seq, $checksum, 0) . $payload;
    }
    
    private function updateSession($clientKey, $clientInfo) {
        if (!isset($this->sessions[$clientKey])) {
            $this->sessions[$clientKey] = [
                'address' => $clientInfo['address'],
                'port' => $clientInfo['port'],
                'created_at' => time(),
                'last_seen' => time(),
                'next_seq' => 1,
                'expected_seq' => 1,
                'connected' => false
            ];
            
            $this->sequenceNumbers[$clientKey] = 1;
        } else {
            $this->sessions[$clientKey]['last_seen'] = time();
        }
    }
    
    private function handleConnect($server, $clientInfo, $packet) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        $this->sessions[$clientKey]['connected'] = true;
        $this->sessions[$clientKey]['expected_seq'] = $packet['seq'] + 1;
        
        // 发送连接确认
        $ackPacket = $this->createPacket(self::PACKET_TYPE_ACK, $packet['seq'], 'CONNECTED');
        $this->sendPacket($server, $clientInfo, $ackPacket);
        
        echo "客户端 {$clientKey} 已连接\n";
    }
    
    private function handleData($server, $clientInfo, $packet) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        if (!$this->sessions[$clientKey]['connected']) {
            echo "收到未连接客户端的数据包: {$clientKey}\n";
            return;
        }
        
        $expectedSeq = $this->sessions[$clientKey]['expected_seq'];
        
        if ($packet['seq'] === $expectedSeq) {
            // 按序到达的数据包
            $this->sessions[$clientKey]['expected_seq']++;
            
            // 发送 ACK
            $ackPacket = $this->createPacket(self::PACKET_TYPE_ACK, $packet['seq']);
            $this->sendPacket($server, $clientInfo, $ackPacket);
            
            // 处理数据
            $this->processData($server, $clientInfo, $packet['payload']);
            
        } elseif ($packet['seq'] < $expectedSeq) {
            // 重复数据包，发送 ACK
            $ackPacket = $this->createPacket(self::PACKET_TYPE_ACK, $packet['seq']);
            $this->sendPacket($server, $clientInfo, $ackPacket);
            
        } else {
            // 乱序数据包，暂时忽略
            echo "收到乱序数据包: 期望={$expectedSeq}, 实际={$packet['seq']}\n";
        }
    }
    
    private function handleAck($clientInfo, $packet) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        // 移除已确认的数据包
        if (isset($this->pendingAcks[$clientKey][$packet['seq']])) {
            unset($this->pendingAcks[$clientKey][$packet['seq']]);
            echo "收到 ACK: 序号={$packet['seq']}, 客户端={$clientKey}\n";
        }
    }
    
    private function handleHeartbeat($server, $clientInfo, $packet) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        // 发送心跳响应
        $responsePacket = $this->createPacket(self::PACKET_TYPE_HEARTBEAT, $packet['seq'], 'PONG');
        $this->sendPacket($server, $clientInfo, $responsePacket);
        
        echo "心跳: {$clientKey}\n";
    }
    
    private function handleDisconnect($clientInfo, $packet) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        if (isset($this->sessions[$clientKey])) {
            $this->sessions[$clientKey]['connected'] = false;
            echo "客户端 {$clientKey} 断开连接\n";
        }
    }
    
    private function processData($server, $clientInfo, $data) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        echo "处理数据: {$data} (来自 {$clientKey})\n";
        
        // 简单的回显处理
        $response = "ECHO: {$data}";
        $this->sendReliableData($server, $clientInfo, $response);
    }
    
    public function sendReliableData($server, $clientInfo, $data) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];
        
        if (!isset($this->sessions[$clientKey]) || !$this->sessions[$clientKey]['connected']) {
            echo "尝试向未连接的客户端发送数据: {$clientKey}\n";
            return false;
        }
        
        $seq = $this->getNextSequence($clientKey);
        $packet = $this->createPacket(self::PACKET_TYPE_DATA, $seq, $data);
        
        // 记录待确认的数据包
        if (!isset($this->pendingAcks[$clientKey])) {
            $this->pendingAcks[$clientKey] = [];
        }
        
        $this->pendingAcks[$clientKey][$seq] = [
            'packet' => $packet,
            'client_info' => $clientInfo,
            'send_time' => time(),
            'retry_count' => 0
        ];
        
        return $this->sendPacket($server, $clientInfo, $packet);
    }
    
    private function sendPacket($server, $clientInfo, $packet) {
        return $server->sendto($clientInfo['address'], $clientInfo['port'], $packet);
    }
    
    private function getNextSequence($clientKey) {
        if (!isset($this->sequenceNumbers[$clientKey])) {
            $this->sequenceNumbers[$clientKey] = 1;
        }
        
        return $this->sequenceNumbers[$clientKey]++;
    }
    
    private function handleRetransmit() {
        $now = time();
        
        foreach ($this->pendingAcks as $clientKey => $packets) {
            foreach ($packets as $seq => $packetInfo) {
                if (($now - $packetInfo['send_time']) >= 1) { // 1秒超时
                    if ($packetInfo['retry_count'] < self::MAX_RETRIES) {
                        // 重传
                        $this->sendPacket($this->server, $packetInfo['client_info'], $packetInfo['packet']);
                        $this->pendingAcks[$clientKey][$seq]['send_time'] = $now;
                        $this->pendingAcks[$clientKey][$seq]['retry_count']++;
                        
                        echo "重传数据包: 序号={$seq}, 客户端={$clientKey}, 重试次数={$packetInfo['retry_count']}\n";
                    } else {
                        // 超过最大重试次数，放弃
                        unset($this->pendingAcks[$clientKey][$seq]);
                        echo "放弃重传: 序号={$seq}, 客户端={$clientKey}\n";
                    }
                }
            }
        }
    }
    
    private function cleanupSessions() {
        $now = time();
        
        foreach ($this->sessions as $clientKey => $session) {
            if (($now - $session['last_seen']) > (self::SESSION_TIMEOUT / 1000)) {
                unset($this->sessions[$clientKey]);
                unset($this->pendingAcks[$clientKey]);
                unset($this->sequenceNumbers[$clientKey]);
                
                echo "清理超时会话: {$clientKey}\n";
            }
        }
    }
    
    public function start() {
        $this->server->start();
    }
}

$server = new ReliableUDPServer('0.0.0.0', 9502);
$server->start();
?>
```

### 14.2.2 UDP 组播服务器

```php
<?php
// multicast_udp_server.php

use Swoole\Server;

class MulticastUDPServer {
    private $server;
    private $multicastGroups = [];
    private $clients = [];

    public function __construct($host = '0.0.0.0', $port = 9503) {
        $this->server = new Server($host, $port, SWOOLE_PROCESS, SWOOLE_SOCK_UDP);
        $this->setupServer();
        $this->setupEvents();
    }

    private function setupServer() {
        $this->server->set([
            'worker_num' => 2,
            'max_conn' => 10000,
        ]);
    }

    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('packet', [$this, 'onPacket']);
    }

    public function onStart($server) {
        echo "组播 UDP 服务器启动成功，监听 {$server->host}:{$server->port}\n";

        // 创建默认组播组
        $this->createMulticastGroup('general', '*************', 9504);
        $this->createMulticastGroup('news', '*************', 9505);
        $this->createMulticastGroup('sports', '*************', 9506);
    }

    public function onPacket($server, $data, $clientInfo) {
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];

        // 更新客户端信息
        $this->clients[$clientKey] = [
            'address' => $clientInfo['address'],
            'port' => $clientInfo['port'],
            'last_seen' => time(),
            'groups' => $this->clients[$clientKey]['groups'] ?? []
        ];

        $message = json_decode($data, true);

        if (!$message || !isset($message['type'])) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '无效的消息格式'
            ]);
            return;
        }

        switch ($message['type']) {
            case 'list_groups':
                $this->handleListGroups($server, $clientInfo);
                break;
            case 'join_group':
                $this->handleJoinGroup($server, $clientInfo, $message);
                break;
            case 'leave_group':
                $this->handleLeaveGroup($server, $clientInfo, $message);
                break;
            case 'send_multicast':
                $this->handleSendMulticast($server, $clientInfo, $message);
                break;
            case 'get_group_members':
                $this->handleGetGroupMembers($server, $clientInfo, $message);
                break;
            default:
                $this->sendResponse($server, $clientInfo, [
                    'type' => 'error',
                    'message' => '未知的消息类型'
                ]);
                break;
        }
    }

    private function createMulticastGroup($name, $multicastAddress, $port) {
        $this->multicastGroups[$name] = [
            'name' => $name,
            'multicast_address' => $multicastAddress,
            'port' => $port,
            'members' => [],
            'created_at' => time(),
            'message_count' => 0
        ];

        echo "创建组播组: {$name} ({$multicastAddress}:{$port})\n";
    }

    private function handleListGroups($server, $clientInfo) {
        $groups = [];

        foreach ($this->multicastGroups as $name => $group) {
            $groups[] = [
                'name' => $name,
                'multicast_address' => $group['multicast_address'],
                'port' => $group['port'],
                'member_count' => count($group['members']),
                'message_count' => $group['message_count']
            ];
        }

        $this->sendResponse($server, $clientInfo, [
            'type' => 'group_list',
            'groups' => $groups
        ]);
    }

    private function handleJoinGroup($server, $clientInfo, $message) {
        $groupName = $message['group'] ?? '';

        if (empty($groupName)) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组名不能为空'
            ]);
            return;
        }

        if (!isset($this->multicastGroups[$groupName])) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组不存在'
            ]);
            return;
        }

        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];

        // 添加客户端到组
        if (!in_array($clientKey, $this->multicastGroups[$groupName]['members'])) {
            $this->multicastGroups[$groupName]['members'][] = $clientKey;
        }

        // 更新客户端的组列表
        if (!in_array($groupName, $this->clients[$clientKey]['groups'])) {
            $this->clients[$clientKey]['groups'][] = $groupName;
        }

        $this->sendResponse($server, $clientInfo, [
            'type' => 'join_success',
            'group' => $groupName,
            'multicast_address' => $this->multicastGroups[$groupName]['multicast_address'],
            'port' => $this->multicastGroups[$groupName]['port']
        ]);

        // 通知组内其他成员
        $this->sendToGroup($server, $groupName, [
            'type' => 'member_joined',
            'group' => $groupName,
            'member' => $clientKey
        ], $clientKey);

        echo "客户端 {$clientKey} 加入组 {$groupName}\n";
    }

    private function handleLeaveGroup($server, $clientInfo, $message) {
        $groupName = $message['group'] ?? '';
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];

        if (empty($groupName)) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组名不能为空'
            ]);
            return;
        }

        if (!isset($this->multicastGroups[$groupName])) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组不存在'
            ]);
            return;
        }

        // 从组中移除客户端
        $this->multicastGroups[$groupName]['members'] = array_filter(
            $this->multicastGroups[$groupName]['members'],
            function($member) use ($clientKey) {
                return $member !== $clientKey;
            }
        );

        // 从客户端的组列表中移除
        if (isset($this->clients[$clientKey])) {
            $this->clients[$clientKey]['groups'] = array_filter(
                $this->clients[$clientKey]['groups'],
                function($group) use ($groupName) {
                    return $group !== $groupName;
                }
            );
        }

        $this->sendResponse($server, $clientInfo, [
            'type' => 'leave_success',
            'group' => $groupName
        ]);

        // 通知组内其他成员
        $this->sendToGroup($server, $groupName, [
            'type' => 'member_left',
            'group' => $groupName,
            'member' => $clientKey
        ]);

        echo "客户端 {$clientKey} 离开组 {$groupName}\n";
    }

    private function handleSendMulticast($server, $clientInfo, $message) {
        $groupName = $message['group'] ?? '';
        $content = $message['content'] ?? '';
        $clientKey = $clientInfo['address'] . ':' . $clientInfo['port'];

        if (empty($groupName) || empty($content)) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组名和消息内容不能为空'
            ]);
            return;
        }

        if (!isset($this->multicastGroups[$groupName])) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组不存在'
            ]);
            return;
        }

        // 检查客户端是否在组内
        if (!in_array($clientKey, $this->multicastGroups[$groupName]['members'])) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '您不在该组内'
            ]);
            return;
        }

        // 发送组播消息
        $multicastMessage = [
            'type' => 'multicast_message',
            'group' => $groupName,
            'from' => $clientKey,
            'content' => $content,
            'timestamp' => time()
        ];

        $count = $this->sendToGroup($server, $groupName, $multicastMessage);
        $this->multicastGroups[$groupName]['message_count']++;

        $this->sendResponse($server, $clientInfo, [
            'type' => 'multicast_sent',
            'group' => $groupName,
            'recipients' => $count
        ]);

        echo "组播消息: {$clientKey} -> {$groupName}: {$content}\n";
    }

    private function handleGetGroupMembers($server, $clientInfo, $message) {
        $groupName = $message['group'] ?? '';

        if (empty($groupName)) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组名不能为空'
            ]);
            return;
        }

        if (!isset($this->multicastGroups[$groupName])) {
            $this->sendResponse($server, $clientInfo, [
                'type' => 'error',
                'message' => '组不存在'
            ]);
            return;
        }

        $this->sendResponse($server, $clientInfo, [
            'type' => 'group_members',
            'group' => $groupName,
            'members' => $this->multicastGroups[$groupName]['members']
        ]);
    }

    private function sendToGroup($server, $groupName, $message, $excludeClient = null) {
        if (!isset($this->multicastGroups[$groupName])) {
            return 0;
        }

        $count = 0;
        $messageJson = json_encode($message);

        foreach ($this->multicastGroups[$groupName]['members'] as $memberKey) {
            if ($memberKey === $excludeClient) {
                continue;
            }

            if (isset($this->clients[$memberKey])) {
                $client = $this->clients[$memberKey];
                if ($server->sendto($client['address'], $client['port'], $messageJson)) {
                    $count++;
                }
            }
        }

        return $count;
    }

    private function sendResponse($server, $clientInfo, $message) {
        $messageJson = json_encode($message);
        return $server->sendto($clientInfo['address'], $clientInfo['port'], $messageJson);
    }

    public function start() {
        $this->server->start();
    }
}

$server = new MulticastUDPServer('0.0.0.0', 9503);
$server->start();
?>
```

## 14.3 UDP 应用场景

### 14.3.1 DNS 服务器实现

```php
<?php
// dns_server.php

use Swoole\Server;

class SimpleDNSServer {
    private $server;
    private $records = [];
    private $stats = [
        'queries' => 0,
        'responses' => 0,
        'errors' => 0
    ];

    // DNS 记录类型
    const TYPE_A = 1;
    const TYPE_NS = 2;
    const TYPE_CNAME = 5;
    const TYPE_MX = 15;
    const TYPE_TXT = 16;
    const TYPE_AAAA = 28;

    // DNS 响应码
    const RCODE_NOERROR = 0;
    const RCODE_NXDOMAIN = 3;
    const RCODE_REFUSED = 5;

    public function __construct($host = '0.0.0.0', $port = 53) {
        $this->server = new Server($host, $port, SWOOLE_PROCESS, SWOOLE_SOCK_UDP);
        $this->setupServer();
        $this->setupEvents();
        $this->loadRecords();
    }

    private function setupServer() {
        $this->server->set([
            'worker_num' => 2,
            'max_conn' => 10000,
        ]);
    }

    private function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('packet', [$this, 'onPacket']);
    }

    private function loadRecords() {
        // 加载 DNS 记录
        $this->records = [
            'example.com' => [
                'A' => ['*************'],
                'MX' => ['10 mail.example.com'],
                'TXT' => ['v=spf1 include:_spf.google.com ~all']
            ],
            'www.example.com' => [
                'A' => ['*************']
            ],
            'mail.example.com' => [
                'A' => ['*************']
            ],
            'test.local' => [
                'A' => ['127.0.0.1']
            ]
        ];

        echo "已加载 " . count($this->records) . " 个 DNS 记录\n";
    }

    public function onStart($server) {
        echo "DNS 服务器启动成功，监听 {$server->host}:{$server->port}\n";
        echo "注意：运行 DNS 服务器通常需要 root 权限\n";
    }

    public function onPacket($server, $data, $clientInfo) {
        $this->stats['queries']++;

        try {
            $query = $this->parseDNSQuery($data);

            if (!$query) {
                $this->stats['errors']++;
                return;
            }

            echo "DNS 查询: {$query['name']} (类型: {$query['type']}) 来自 {$clientInfo['address']}\n";

            $response = $this->buildDNSResponse($query, $data);

            if ($response) {
                $server->sendto($clientInfo['address'], $clientInfo['port'], $response);
                $this->stats['responses']++;
            } else {
                $this->stats['errors']++;
            }

        } catch (Exception $e) {
            echo "DNS 查询处理错误: " . $e->getMessage() . "\n";
            $this->stats['errors']++;
        }
    }

    private function parseDNSQuery($data) {
        if (strlen($data) < 12) {
            return null;
        }

        // 解析 DNS 头部
        $header = unpack('nid/nflags/nqdcount/nancount/nnscount/narcount', substr($data, 0, 12));

        if ($header['qdcount'] !== 1) {
            return null; // 只支持单个查询
        }

        // 解析查询名称
        $offset = 12;
        $name = $this->parseDNSName($data, $offset);

        if ($name === null) {
            return null;
        }

        // 解析查询类型和类
        if (strlen($data) < $offset + 4) {
            return null;
        }

        $queryInfo = unpack('ntype/nclass', substr($data, $offset, 4));

        return [
            'id' => $header['id'],
            'name' => $name,
            'type' => $queryInfo['type'],
            'class' => $queryInfo['class']
        ];
    }

    private function parseDNSName($data, &$offset) {
        $name = '';
        $jumped = false;
        $maxJumps = 5;
        $jumps = 0;

        while ($offset < strlen($data)) {
            $length = ord($data[$offset]);

            if ($length === 0) {
                $offset++;
                break;
            }

            if (($length & 0xC0) === 0xC0) {
                // 压缩指针
                if (!$jumped) {
                    $jumped = true;
                }

                $jumps++;
                if ($jumps > $maxJumps) {
                    return null; // 防止无限循环
                }

                $pointer = (($length & 0x3F) << 8) | ord($data[$offset + 1]);
                $offset = $pointer;
                continue;
            }

            $offset++;

            if ($offset + $length > strlen($data)) {
                return null;
            }

            if (!empty($name)) {
                $name .= '.';
            }

            $name .= substr($data, $offset, $length);
            $offset += $length;
        }

        return $name;
    }

    private function buildDNSResponse($query, $originalData) {
        $name = strtolower($query['name']);
        $type = $query['type'];

        // 构建响应头部
        $flags = 0x8000; // QR=1 (响应)
        $rcode = self::RCODE_NXDOMAIN;
        $ancount = 0;

        $answers = '';

        // 查找记录
        if (isset($this->records[$name])) {
            $typeMap = [
                self::TYPE_A => 'A',
                self::TYPE_MX => 'MX',
                self::TYPE_TXT => 'TXT',
                self::TYPE_CNAME => 'CNAME'
            ];

            $typeStr = $typeMap[$type] ?? null;

            if ($typeStr && isset($this->records[$name][$typeStr])) {
                $rcode = self::RCODE_NOERROR;
                $flags |= 0x0400; // AA=1 (权威回答)

                foreach ($this->records[$name][$typeStr] as $record) {
                    $answers .= $this->buildDNSAnswer($query['name'], $type, $record);
                    $ancount++;
                }
            }
        }

        $flags |= $rcode;

        // 构建响应
        $response = pack('nnnnnn',
            $query['id'],
            $flags,
            1, // QDCOUNT
            $ancount, // ANCOUNT
            0, // NSCOUNT
            0  // ARCOUNT
        );

        // 添加原始查询
        $response .= substr($originalData, 12);

        // 添加答案
        $response .= $answers;

        return $response;
    }

    private function buildDNSAnswer($name, $type, $data) {
        $answer = $this->encodeDNSName($name);
        $answer .= pack('nnNn', $type, 1, 300, 0); // 类型、类、TTL、数据长度占位符

        $rdata = '';

        switch ($type) {
            case self::TYPE_A:
                $rdata = inet_pton($data);
                break;
            case self::TYPE_TXT:
                $rdata = chr(strlen($data)) . $data;
                break;
            case self::TYPE_MX:
                $parts = explode(' ', $data, 2);
                $priority = intval($parts[0]);
                $exchange = $parts[1];
                $rdata = pack('n', $priority) . $this->encodeDNSName($exchange);
                break;
        }

        // 更新数据长度
        $answer = substr($answer, 0, -2) . pack('n', strlen($rdata)) . $rdata;

        return $answer;
    }

    private function encodeDNSName($name) {
        $encoded = '';
        $parts = explode('.', $name);

        foreach ($parts as $part) {
            $encoded .= chr(strlen($part)) . $part;
        }

        $encoded .= "\0";

        return $encoded;
    }

    public function getStats() {
        return $this->stats;
    }

    public function start() {
        $this->server->start();
    }
}

// 注意：运行 DNS 服务器需要 root 权限
if (posix_getuid() !== 0) {
    echo "警告：DNS 服务器通常需要 root 权限才能绑定到端口 53\n";
    echo "您可以使用其他端口进行测试，例如 5353\n";
    $server = new SimpleDNSServer('0.0.0.0', 5353);
} else {
    $server = new SimpleDNSServer('0.0.0.0', 53);
}

$server->start();
?>
```

## 本章练习

### 练习 1：UDP 文件传输
实现基于 UDP 的文件传输系统：

```php
<?php
// udp_file_transfer.php

class UDPFileTransfer {
    // 实现功能：
    // 1. 文件分片传输
    // 2. 丢包重传机制
    // 3. 传输进度监控
    // 4. 文件完整性校验
    // 5. 并发传输控制
}
?>
```

### 练习 2：UDP 游戏服务器
创建基于 UDP 的实时游戏服务器：

```php
<?php
// udp_game_server.php

class UDPGameServer {
    // 实现功能：
    // 1. 玩家状态同步
    // 2. 游戏事件广播
    // 3. 延迟补偿
    // 4. 反作弊检测
    // 5. 房间管理系统
}
?>
```

### 练习 3：UDP 负载均衡器
实现 UDP 负载均衡器：

```php
<?php
// udp_load_balancer.php

class UDPLoadBalancer {
    // 实现功能：
    // 1. 多种负载均衡算法
    // 2. 健康检查
    // 3. 故障转移
    // 4. 会话保持
    // 5. 流量统计
}
?>
```

### 练习 4：UDP 性能监控
开发 UDP 性能监控工具：

```php
<?php
// udp_monitor.php

class UDPMonitor {
    // 实现功能：
    // 1. 丢包率统计
    // 2. 延迟测量
    // 3. 吞吐量监控
    // 4. 网络质量评估
    // 5. 性能报告生成
}
?>
```

## 本章小结

本章详细介绍了 Swoole UDP 服务器的开发，从基础概念到高级应用，涵盖了 UDP 服务器开发的各个方面。

**关键要点：**

- **UDP 特性**：无连接、不可靠、低开销、快速
- **可靠性保证**：序列号、确认机制、重传机制
- **组播通信**：一对多通信、组管理、消息分发
- **应用场景**：DNS、游戏、实时通信、监控
- **性能优化**：数据包处理、内存管理、并发控制

**设计原则：**

1. **简单高效**：充分利用 UDP 的低开销特性
2. **可靠性设计**：在应用层实现必要的可靠性保证
3. **状态管理**：合理管理连接状态和会话信息
4. **错误处理**：完善的异常处理和恢复机制
5. **性能优化**：针对 UDP 特性进行优化

**最佳实践：**

1. 根据应用需求选择是否需要可靠性保证
2. 实现适当的超时和重传机制
3. 使用校验和验证数据完整性
4. 合理设计数据包格式
5. 考虑网络环境的影响

**UDP vs TCP 对比：**

| 特性 | UDP | TCP |
|------|-----|-----|
| 连接性 | 无连接 | 面向连接 |
| 可靠性 | 不可靠 | 可靠 |
| 开销 | 低 | 高 |
| 速度 | 快 | 相对慢 |
| 应用场景 | 实时通信、游戏、DNS | Web、文件传输、邮件 |

**下一章预告：**
下一章我们将学习进程管理，了解 Swoole 的多进程架构和进程间通信机制。
