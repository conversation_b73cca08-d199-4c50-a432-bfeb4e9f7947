# 第8章：中间件机制

## 学习目标
- 理解中间件的概念和作用
- 掌握中间件的设计模式和实现方法
- 学会创建各种类型的中间件
- 了解中间件的执行顺序和异常处理

## 8.1 中间件基础概念

### 8.1.1 什么是中间件

中间件是在请求到达最终处理器之前或响应返回客户端之前执行的代码层。它采用洋葱模型（Onion Model），允许我们在请求处理的不同阶段插入自定义逻辑。

```php
<?php
// middleware_concept.php

use Swoole\Http\Server;

// 中间件接口
interface MiddlewareInterface {
    public function handle($request, $response, $next);
}

// 基础中间件类
abstract class Middleware implements MiddlewareInterface {
    abstract public function handle($request, $response, $next);
}

// 日志中间件
class LogMiddleware extends Middleware {
    public function handle($request, $response, $next) {
        $start = microtime(true);
        $method = $request->server['request_method'];
        $uri = $request->server['request_uri'];
        
        echo "[开始] {$method} {$uri} - " . date('Y-m-d H:i:s') . "\n";
        
        // 调用下一个中间件
        $result = $next($request, $response);
        
        $duration = round((microtime(true) - $start) * 1000, 2);
        echo "[结束] {$method} {$uri} - 耗时: {$duration}ms\n";
        
        return $result;
    }
}

// 认证中间件
class AuthMiddleware extends Middleware {
    public function handle($request, $response, $next) {
        $token = $request->header['authorization'] ?? '';
        
        echo "[认证] 检查 Token: {$token}\n";
        
        if (empty($token) || $token !== 'Bearer valid-token') {
            $response->status(401);
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['error' => 'Unauthorized']));
            return false;
        }
        
        echo "[认证] Token 验证通过\n";
        return $next($request, $response);
    }
}

// CORS 中间件
class CorsMiddleware extends Middleware {
    public function handle($request, $response, $next) {
        echo "[CORS] 设置跨域头部\n";
        
        $response->header("Access-Control-Allow-Origin", "*");
        $response->header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        $response->header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        
        // 处理预检请求
        if ($request->server['request_method'] === 'OPTIONS') {
            $response->status(200);
            $response->end();
            return true;
        }
        
        return $next($request, $response);
    }
}

// 中间件管理器
class MiddlewareManager {
    private $middlewares = [];
    
    public function add($middleware) {
        $this->middlewares[] = $middleware;
    }
    
    public function handle($request, $response, $finalHandler) {
        $index = 0;
        $middlewares = $this->middlewares;
        
        $next = function($request, $response) use (&$next, &$index, $middlewares, $finalHandler) {
            if ($index < count($middlewares)) {
                $middleware = $middlewares[$index++];
                return $middleware->handle($request, $response, $next);
            } else {
                return $finalHandler($request, $response);
            }
        };
        
        return $next($request, $response);
    }
}

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $manager = new MiddlewareManager();
    
    // 添加中间件（执行顺序很重要）
    $manager->add(new LogMiddleware());
    $manager->add(new CorsMiddleware());
    
    $uri = $request->server['request_uri'];
    
    // 某些路由需要认证
    if (strpos($uri, '/protected') === 0) {
        $manager->add(new AuthMiddleware());
    }
    
    // 最终处理器
    $finalHandler = function($request, $response) use ($uri) {
        $response->header("Content-Type", "application/json");
        
        if ($uri === '/') {
            $response->end(json_encode(['message' => '公开页面']));
        } elseif ($uri === '/protected/profile') {
            $response->end(json_encode(['message' => '受保护的用户资料']));
        } elseif ($uri === '/protected/admin') {
            $response->end(json_encode(['message' => '管理员页面']));
        } else {
            $response->status(404);
            $response->end(json_encode(['error' => 'Not Found']));
        }
    };
    
    $manager->handle($request, $response, $finalHandler);
});

echo "中间件演示服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "测试路由:\n";
echo "  GET / - 公开页面\n";
echo "  GET /protected/profile - 需要认证 (Authorization: Bearer valid-token)\n";
echo "  OPTIONS /protected/profile - CORS 预检请求\n";
$server->start();
?>
```

### 8.1.2 中间件执行流程

```php
<?php
// middleware_flow.php

class MiddlewareFlow {
    public static function demonstrate() {
        echo "=== 中间件执行流程演示 ===\n\n";
        
        // 模拟中间件栈
        $middlewares = [
            function($request, $next) {
                echo "1. 中间件 A - 请求前处理\n";
                $result = $next($request);
                echo "6. 中间件 A - 响应后处理\n";
                return $result;
            },
            function($request, $next) {
                echo "2. 中间件 B - 请求前处理\n";
                $result = $next($request);
                echo "5. 中间件 B - 响应后处理\n";
                return $result;
            },
            function($request, $next) {
                echo "3. 中间件 C - 请求前处理\n";
                $result = $next($request);
                echo "4. 中间件 C - 响应后处理\n";
                return $result;
            }
        ];
        
        // 最终处理器
        $handler = function($request) {
            echo ">>> 核心处理器执行\n";
            return "处理结果";
        };
        
        // 构建执行链
        $index = 0;
        $next = function($request) use (&$next, &$index, $middlewares, $handler) {
            if ($index < count($middlewares)) {
                $middleware = $middlewares[$index++];
                return $middleware($request, $next);
            } else {
                return $handler($request);
            }
        };
        
        // 执行
        $result = $next("模拟请求");
        echo "\n最终结果: {$result}\n";
    }
}

MiddlewareFlow::demonstrate();
?>
```

## 8.2 常用中间件实现

### 8.2.1 认证和授权中间件

```php
<?php
// auth_middleware.php

use Swoole\Http\Server;

// JWT 工具类（简化版）
class JWT {
    private static $secret = 'your-secret-key';
    
    public static function encode($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $headerEncoded = base64url_encode($header);
        $payloadEncoded = base64url_encode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, self::$secret, true);
        $signatureEncoded = base64url_encode($signature);
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    public static function decode($jwt) {
        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
        
        $signature = base64url_decode($signatureEncoded);
        $expectedSignature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, self::$secret, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        return json_decode(base64url_decode($payloadEncoded), true);
    }
}

function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function base64url_decode($data) {
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}

// 认证中间件
class JWTAuthMiddleware {
    public function handle($request, $response, $next) {
        $authHeader = $request->header['authorization'] ?? '';
        
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $this->unauthorized($response, 'Missing or invalid authorization header');
        }
        
        $token = $matches[1];
        $payload = JWT::decode($token);
        
        if (!$payload) {
            return $this->unauthorized($response, 'Invalid token');
        }
        
        // 检查过期时间
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return $this->unauthorized($response, 'Token expired');
        }
        
        // 将用户信息添加到请求中
        $request->user = $payload;
        
        return $next($request, $response);
    }
    
    private function unauthorized($response, $message) {
        $response->status(401);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => $message]));
        return false;
    }
}

// 权限检查中间件
class PermissionMiddleware {
    private $requiredPermission;
    
    public function __construct($permission) {
        $this->requiredPermission = $permission;
    }
    
    public function handle($request, $response, $next) {
        if (!isset($request->user)) {
            return $this->forbidden($response, 'User not authenticated');
        }
        
        $userPermissions = $request->user['permissions'] ?? [];
        
        if (!in_array($this->requiredPermission, $userPermissions)) {
            return $this->forbidden($response, 'Insufficient permissions');
        }
        
        return $next($request, $response);
    }
    
    private function forbidden($response, $message) {
        $response->status(403);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => $message]));
        return false;
    }
}

// 角色检查中间件
class RoleMiddleware {
    private $allowedRoles;
    
    public function __construct($roles) {
        $this->allowedRoles = is_array($roles) ? $roles : [$roles];
    }
    
    public function handle($request, $response, $next) {
        if (!isset($request->user)) {
            return $this->forbidden($response, 'User not authenticated');
        }
        
        $userRole = $request->user['role'] ?? '';
        
        if (!in_array($userRole, $this->allowedRoles)) {
            return $this->forbidden($response, 'Access denied for role: ' . $userRole);
        }
        
        return $next($request, $response);
    }
    
    private function forbidden($response, $message) {
        $response->status(403);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['error' => $message]));
        return false;
    }
}

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];
    $method = $request->server['request_method'];
    
    // 登录端点
    if ($uri === '/login' && $method === 'POST') {
        $data = json_decode($request->rawContent(), true);
        $username = $data['username'] ?? '';
        $password = $data['password'] ?? '';
        
        // 简单的用户验证
        $users = [
            'admin' => ['password' => 'admin123', 'role' => 'admin', 'permissions' => ['read', 'write', 'delete']],
            'user' => ['password' => 'user123', 'role' => 'user', 'permissions' => ['read']],
        ];
        
        if (isset($users[$username]) && $users[$username]['password'] === $password) {
            $payload = [
                'username' => $username,
                'role' => $users[$username]['role'],
                'permissions' => $users[$username]['permissions'],
                'exp' => time() + 3600 // 1小时过期
            ];
            
            $token = JWT::encode($payload);
            
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['token' => $token]));
        } else {
            $response->status(401);
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['error' => 'Invalid credentials']));
        }
        return;
    }
    
    // 中间件管理
    $middlewares = [];
    
    // 需要认证的路由
    if (strpos($uri, '/api/') === 0) {
        $middlewares[] = new JWTAuthMiddleware();
        
        // 管理员路由
        if (strpos($uri, '/api/admin/') === 0) {
            $middlewares[] = new RoleMiddleware('admin');
        }
        
        // 需要写权限的路由
        if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
            $middlewares[] = new PermissionMiddleware('write');
        }
    }
    
    // 执行中间件链
    $index = 0;
    $next = function($request, $response) use (&$next, &$index, $middlewares, $uri) {
        if ($index < count($middlewares)) {
            $middleware = $middlewares[$index++];
            return $middleware->handle($request, $response, $next);
        } else {
            return handleRequest($request, $response, $uri);
        }
    };
    
    $next($request, $response);
});

function handleRequest($request, $response, $uri) {
    $response->header("Content-Type", "application/json");
    
    switch ($uri) {
        case '/':
            $response->end(json_encode(['message' => '公开页面']));
            break;
            
        case '/api/profile':
            $user = $request->user ?? [];
            $response->end(json_encode(['message' => '用户资料', 'user' => $user]));
            break;
            
        case '/api/admin/users':
            $response->end(json_encode(['message' => '用户管理', 'users' => ['admin', 'user']]));
            break;
            
        default:
            $response->status(404);
            $response->end(json_encode(['error' => 'Not Found']));
            break;
    }
}

echo "认证中间件服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "使用方法:\n";
echo "1. POST /login 登录获取 token\n";
echo "   Body: {\"username\":\"admin\",\"password\":\"admin123\"}\n";
echo "2. GET /api/profile 查看资料 (需要 Authorization: Bearer <token>)\n";
echo "3. GET /api/admin/users 管理员功能 (需要 admin 角色)\n";
$server->start();
?>
```

### 8.2.2 限流和缓存中间件

```php
<?php
// rate_limit_middleware.php

use Swoole\Http\Server;
use Swoole\Table;

// 限流中间件
class RateLimitMiddleware {
    private $table;
    private $maxRequests;
    private $timeWindow;

    public function __construct($maxRequests = 100, $timeWindow = 60) {
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow;

        // 创建内存表存储限流信息
        $this->table = new Table(1024);
        $this->table->column('count', Table::TYPE_INT);
        $this->table->column('reset_time', Table::TYPE_INT);
        $this->table->create();
    }

    public function handle($request, $response, $next) {
        $clientIp = $this->getClientIp($request);
        $now = time();

        $record = $this->table->get($clientIp);

        if (!$record) {
            // 首次访问
            $this->table->set($clientIp, [
                'count' => 1,
                'reset_time' => $now + $this->timeWindow
            ]);

            $this->setRateLimitHeaders($response, 1, $now + $this->timeWindow);
            return $next($request, $response);
        }

        if ($now > $record['reset_time']) {
            // 时间窗口已过，重置计数
            $this->table->set($clientIp, [
                'count' => 1,
                'reset_time' => $now + $this->timeWindow
            ]);

            $this->setRateLimitHeaders($response, 1, $now + $this->timeWindow);
            return $next($request, $response);
        }

        if ($record['count'] >= $this->maxRequests) {
            // 超过限制
            $this->setRateLimitHeaders($response, $record['count'], $record['reset_time']);

            $response->status(429);
            $response->header("Content-Type", "application/json");
            $response->end(json_encode([
                'error' => 'Too Many Requests',
                'message' => "Rate limit exceeded. Try again in " . ($record['reset_time'] - $now) . " seconds."
            ]));
            return false;
        }

        // 增加计数
        $this->table->set($clientIp, [
            'count' => $record['count'] + 1,
            'reset_time' => $record['reset_time']
        ]);

        $this->setRateLimitHeaders($response, $record['count'] + 1, $record['reset_time']);
        return $next($request, $response);
    }

    private function getClientIp($request) {
        return $request->header['x-forwarded-for'] ??
               $request->header['x-real-ip'] ??
               $request->server['remote_addr'] ??
               '127.0.0.1';
    }

    private function setRateLimitHeaders($response, $count, $resetTime) {
        $response->header("X-RateLimit-Limit", $this->maxRequests);
        $response->header("X-RateLimit-Remaining", max(0, $this->maxRequests - $count));
        $response->header("X-RateLimit-Reset", $resetTime);
    }

    public function getStats() {
        $stats = [];
        foreach ($this->table as $ip => $record) {
            $stats[$ip] = $record;
        }
        return $stats;
    }
}

// 缓存中间件
class CacheMiddleware {
    private $cache = [];
    private $ttl;

    public function __construct($ttl = 300) { // 默认5分钟
        $this->ttl = $ttl;
    }

    public function handle($request, $response, $next) {
        $method = $request->server['request_method'];

        // 只缓存 GET 请求
        if ($method !== 'GET') {
            return $next($request, $response);
        }

        $cacheKey = $this->getCacheKey($request);
        $cached = $this->getFromCache($cacheKey);

        if ($cached) {
            // 缓存命中
            $response->header("Content-Type", $cached['content_type']);
            $response->header("X-Cache", "HIT");
            $response->header("X-Cache-TTL", $cached['expires'] - time());
            $response->end($cached['content']);
            return true;
        }

        // 缓存未命中，继续处理
        $response->header("X-Cache", "MISS");

        // 捕获响应内容
        $originalEnd = $response->end;
        $capturedContent = '';
        $capturedContentType = 'text/html';

        // 重写 end 方法来捕获内容
        $response->end = function($content) use (&$capturedContent, &$capturedContentType, $originalEnd, $response, $cacheKey) {
            $capturedContent = $content;

            // 获取 Content-Type
            $headers = $response->header ?? [];
            foreach ($headers as $name => $value) {
                if (strtolower($name) === 'content-type') {
                    $capturedContentType = $value;
                    break;
                }
            }

            // 存储到缓存
            $this->setToCache($cacheKey, $content, $capturedContentType);

            // 调用原始 end 方法
            call_user_func($originalEnd, $content);
        };

        return $next($request, $response);
    }

    private function getCacheKey($request) {
        $uri = $request->server['request_uri'];
        $query = $request->server['query_string'] ?? '';
        return md5($uri . '?' . $query);
    }

    private function getFromCache($key) {
        if (!isset($this->cache[$key])) {
            return null;
        }

        $cached = $this->cache[$key];
        if ($cached['expires'] < time()) {
            unset($this->cache[$key]);
            return null;
        }

        return $cached;
    }

    private function setToCache($key, $content, $contentType) {
        $this->cache[$key] = [
            'content' => $content,
            'content_type' => $contentType,
            'expires' => time() + $this->ttl,
            'created' => time()
        ];
    }

    public function clearCache() {
        $this->cache = [];
    }

    public function getStats() {
        $total = count($this->cache);
        $expired = 0;
        $now = time();

        foreach ($this->cache as $cached) {
            if ($cached['expires'] < $now) {
                $expired++;
            }
        }

        return [
            'total_entries' => $total,
            'expired_entries' => $expired,
            'active_entries' => $total - $expired,
            'memory_usage' => memory_get_usage(true)
        ];
    }
}

// 压缩中间件
class CompressionMiddleware {
    private $minLength;
    private $compressibleTypes;

    public function __construct($minLength = 1024) {
        $this->minLength = $minLength;
        $this->compressibleTypes = [
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'application/json',
            'text/xml',
            'application/xml'
        ];
    }

    public function handle($request, $response, $next) {
        $acceptEncoding = $request->header['accept-encoding'] ?? '';
        $supportsGzip = strpos($acceptEncoding, 'gzip') !== false;

        if (!$supportsGzip) {
            return $next($request, $response);
        }

        // 捕获响应内容
        $originalEnd = $response->end;

        $response->end = function($content) use ($originalEnd, $response) {
            if (strlen($content) < $this->minLength) {
                call_user_func($originalEnd, $content);
                return;
            }

            $contentType = $response->header['content-type'] ?? 'text/html';
            $isCompressible = false;

            foreach ($this->compressibleTypes as $type) {
                if (strpos($contentType, $type) !== false) {
                    $isCompressible = true;
                    break;
                }
            }

            if ($isCompressible) {
                $compressed = gzencode($content, 6);
                $response->header("Content-Encoding", "gzip");
                $response->header("Content-Length", strlen($compressed));
                $response->header("X-Compression-Ratio", round((1 - strlen($compressed) / strlen($content)) * 100, 2) . '%');
                call_user_func($originalEnd, $compressed);
            } else {
                call_user_func($originalEnd, $content);
            }
        };

        return $next($request, $response);
    }
}

$rateLimiter = new RateLimitMiddleware(10, 60); // 每分钟10次请求
$cache = new CacheMiddleware(60); // 缓存1分钟
$compression = new CompressionMiddleware(100); // 超过100字节压缩

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) use ($rateLimiter, $cache, $compression) {
    $uri = $request->server['request_uri'];

    // 统计端点
    if ($uri === '/stats') {
        $stats = [
            'rate_limit' => $rateLimiter->getStats(),
            'cache' => $cache->getStats(),
            'timestamp' => time()
        ];

        $response->header("Content-Type", "application/json");
        $response->end(json_encode($stats, JSON_PRETTY_PRINT));
        return;
    }

    // 清除缓存端点
    if ($uri === '/clear-cache') {
        $cache->clearCache();
        $response->header("Content-Type", "application/json");
        $response->end(json_encode(['message' => 'Cache cleared']));
        return;
    }

    // 中间件链
    $middlewares = [$rateLimiter, $cache, $compression];

    $index = 0;
    $next = function($request, $response) use (&$next, &$index, $middlewares, $uri) {
        if ($index < count($middlewares)) {
            $middleware = $middlewares[$index++];
            return $middleware->handle($request, $response, $next);
        } else {
            return handleRequest($request, $response, $uri);
        }
    };

    $next($request, $response);
});

function handleRequest($request, $response, $uri) {
    switch ($uri) {
        case '/':
            $response->header("Content-Type", "text/html; charset=utf-8");
            $html = str_repeat('<p>这是一个测试页面，用于演示缓存和压缩中间件。</p>', 50);
            $response->end("<!DOCTYPE html><html><head><title>测试页面</title></head><body>{$html}</body></html>");
            break;

        case '/api/data':
            $response->header("Content-Type", "application/json");
            $data = [
                'timestamp' => time(),
                'random' => rand(1000, 9999),
                'data' => array_fill(0, 100, 'test data item')
            ];
            $response->end(json_encode($data));
            break;

        case '/slow':
            // 模拟慢接口
            usleep(500000); // 0.5秒
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['message' => '慢接口响应', 'timestamp' => time()]));
            break;

        default:
            $response->status(404);
            $response->header("Content-Type", "application/json");
            $response->end(json_encode(['error' => 'Not Found']));
            break;
    }
}

echo "限流和缓存中间件服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "测试端点:\n";
echo "  GET / - 测试页面 (缓存+压缩)\n";
echo "  GET /api/data - JSON 数据 (缓存+压缩)\n";
echo "  GET /slow - 慢接口 (缓存效果明显)\n";
echo "  GET /stats - 查看统计信息\n";
echo "  GET /clear-cache - 清除缓存\n";
echo "\n快速访问测试限流: for i in {1..15}; do curl http://127.0.0.1:9501/; done\n";
$server->start();
?>
```

## 8.3 中间件管道和异常处理

### 8.3.1 中间件管道

```php
<?php
// middleware_pipeline.php

use Swoole\Http\Server;

// 中间件管道类
class MiddlewarePipeline {
    private $middlewares = [];
    private $errorHandler;

    public function pipe($middleware) {
        $this->middlewares[] = $middleware;
        return $this;
    }

    public function setErrorHandler($handler) {
        $this->errorHandler = $handler;
        return $this;
    }

    public function process($request, $response, $destination) {
        $index = 0;
        $middlewares = $this->middlewares;

        $next = function($request, $response) use (&$next, &$index, $middlewares, $destination) {
            try {
                if ($index < count($middlewares)) {
                    $middleware = $middlewares[$index++];

                    if (is_callable($middleware)) {
                        return $middleware($request, $response, $next);
                    } elseif (is_object($middleware) && method_exists($middleware, 'handle')) {
                        return $middleware->handle($request, $response, $next);
                    } else {
                        throw new Exception("Invalid middleware");
                    }
                } else {
                    return $destination($request, $response);
                }
            } catch (Throwable $e) {
                if ($this->errorHandler) {
                    return call_user_func($this->errorHandler, $e, $request, $response);
                } else {
                    throw $e;
                }
            }
        };

        return $next($request, $response);
    }
}

// 异常处理中间件
class ExceptionHandlerMiddleware {
    public function handle($request, $response, $next) {
        try {
            return $next($request, $response);
        } catch (HttpException $e) {
            $this->handleHttpException($e, $response);
        } catch (ValidationException $e) {
            $this->handleValidationException($e, $response);
        } catch (Throwable $e) {
            $this->handleGenericException($e, $response);
        }
    }

    private function handleHttpException($e, $response) {
        $response->status($e->getStatusCode());
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'error' => $e->getMessage(),
            'status_code' => $e->getStatusCode()
        ]));
    }

    private function handleValidationException($e, $response) {
        $response->status(422);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'error' => 'Validation failed',
            'errors' => $e->getErrors()
        ]));
    }

    private function handleGenericException($e, $response) {
        error_log("Unhandled exception: " . $e->getMessage() . "\n" . $e->getTraceAsString());

        $response->status(500);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'error' => 'Internal Server Error',
            'message' => 'An unexpected error occurred'
        ]));
    }
}

// 自定义异常类
class HttpException extends Exception {
    private $statusCode;

    public function __construct($message, $statusCode = 500, $previous = null) {
        parent::__construct($message, 0, $previous);
        $this->statusCode = $statusCode;
    }

    public function getStatusCode() {
        return $this->statusCode;
    }
}

class ValidationException extends Exception {
    private $errors;

    public function __construct($errors, $message = 'Validation failed') {
        parent::__construct($message);
        $this->errors = $errors;
    }

    public function getErrors() {
        return $this->errors;
    }
}

// 请求验证中间件
class ValidationMiddleware {
    private $rules;

    public function __construct($rules) {
        $this->rules = $rules;
    }

    public function handle($request, $response, $next) {
        $data = array_merge($request->get ?? [], $request->post ?? []);
        $errors = [];

        foreach ($this->rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $fieldErrors = $this->validateField($field, $value, $rule);

            if (!empty($fieldErrors)) {
                $errors[$field] = $fieldErrors;
            }
        }

        if (!empty($errors)) {
            throw new ValidationException($errors);
        }

        return $next($request, $response);
    }

    private function validateField($field, $value, $rules) {
        $errors = [];
        $ruleList = explode('|', $rules);

        foreach ($ruleList as $rule) {
            $ruleParts = explode(':', $rule);
            $ruleName = $ruleParts[0];
            $ruleValue = $ruleParts[1] ?? null;

            switch ($ruleName) {
                case 'required':
                    if (empty($value)) {
                        $errors[] = "{$field} is required";
                    }
                    break;

                case 'min':
                    if (strlen($value) < $ruleValue) {
                        $errors[] = "{$field} must be at least {$ruleValue} characters";
                    }
                    break;

                case 'max':
                    if (strlen($value) > $ruleValue) {
                        $errors[] = "{$field} must not exceed {$ruleValue} characters";
                    }
                    break;

                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "{$field} must be a valid email address";
                    }
                    break;

                case 'numeric':
                    if (!is_numeric($value)) {
                        $errors[] = "{$field} must be numeric";
                    }
                    break;
            }
        }

        return $errors;
    }
}

// 性能监控中间件
class PerformanceMiddleware {
    public function handle($request, $response, $next) {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $result = $next($request, $response);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $duration = round(($endTime - $startTime) * 1000, 2);
        $memoryUsed = round(($endMemory - $startMemory) / 1024, 2);

        $response->header("X-Response-Time", $duration . 'ms');
        $response->header("X-Memory-Usage", $memoryUsed . 'KB');
        $response->header("X-Peak-Memory", round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB');

        echo "[性能] {$request->server['request_method']} {$request->server['request_uri']} - {$duration}ms, {$memoryUsed}KB\n";

        return $result;
    }
}

$server = new Server("127.0.0.1", 9501);

$server->on("request", function ($request, $response) {
    $uri = $request->server['request_uri'];
    $method = $request->server['request_method'];

    $pipeline = new MiddlewarePipeline();

    // 全局中间件
    $pipeline->pipe(new ExceptionHandlerMiddleware())
             ->pipe(new PerformanceMiddleware());

    // 根据路由添加特定中间件
    if ($uri === '/api/users' && $method === 'POST') {
        $pipeline->pipe(new ValidationMiddleware([
            'name' => 'required|min:2|max:50',
            'email' => 'required|email',
            'age' => 'numeric'
        ]));
    }

    // 设置错误处理器
    $pipeline->setErrorHandler(function($e, $request, $response) {
        error_log("Pipeline error: " . $e->getMessage());

        $response->status(500);
        $response->header("Content-Type", "application/json");
        $response->end(json_encode([
            'error' => 'Pipeline Error',
            'message' => $e->getMessage()
        ]));
    });

    // 最终处理器
    $destination = function($request, $response) use ($uri, $method) {
        switch ($uri) {
            case '/':
                $response->header("Content-Type", "application/json");
                $response->end(json_encode(['message' => '中间件管道演示']));
                break;

            case '/api/users':
                if ($method === 'POST') {
                    $data = array_merge($request->get ?? [], $request->post ?? []);
                    $response->header("Content-Type", "application/json");
                    $response->end(json_encode([
                        'message' => '用户创建成功',
                        'data' => $data
                    ]));
                } else {
                    $response->header("Content-Type", "application/json");
                    $response->end(json_encode(['users' => ['Alice', 'Bob', 'Charlie']]));
                }
                break;

            case '/error':
                throw new HttpException('This is a test error', 400);

            case '/exception':
                throw new Exception('This is an unhandled exception');

            default:
                throw new HttpException('Not Found', 404);
        }
    };

    $pipeline->process($request, $response, $destination);
});

echo "中间件管道服务器启动成功，访问 http://127.0.0.1:9501\n";
echo "测试端点:\n";
echo "  GET / - 基础页面\n";
echo "  GET /api/users - 用户列表\n";
echo "  POST /api/users - 创建用户 (需要验证)\n";
echo "  GET /error - HTTP 异常测试\n";
echo "  GET /exception - 通用异常测试\n";
echo "\n测试验证: curl -X POST http://127.0.0.1:9501/api/users -d 'name=John&email=invalid'\n";
$server->start();
?>
```

## 本章练习

### 练习 1：安全中间件套件
实现一套完整的安全中间件：

```php
<?php
// security_middleware.php

// 实现以下安全中间件：
// 1. CSRF 保护中间件
// 2. XSS 防护中间件
// 3. SQL 注入防护中间件
// 4. 请求大小限制中间件
// 5. IP 白名单/黑名单中间件
// 6. 安全头部中间件

class CSRFMiddleware {
    // 实现 CSRF Token 验证
}

class XSSProtectionMiddleware {
    // 实现 XSS 过滤
}

class SecurityHeadersMiddleware {
    // 实现安全头部设置
    // X-Frame-Options, X-XSS-Protection, X-Content-Type-Options 等
}
?>
```

### 练习 2：API 版本控制中间件
实现 API 版本控制系统：

```php
<?php
// api_versioning.php

class APIVersionMiddleware {
    // 实现功能：
    // 1. 从 Header 或 URL 获取版本信息
    // 2. 版本兼容性检查
    // 3. 版本弃用警告
    // 4. 自动版本升级建议
    // 5. 版本使用统计
}
?>
```

### 练习 3：请求/响应转换中间件
实现数据格式转换中间件：

```php
<?php
// transformation_middleware.php

class RequestTransformMiddleware {
    // 实现功能：
    // 1. 请求数据格式转换 (JSON, XML, Form)
    // 2. 字段名称映射
    // 3. 数据类型转换
    // 4. 默认值填充
}

class ResponseTransformMiddleware {
    // 实现功能：
    // 1. 响应数据格式转换
    // 2. 字段过滤和重命名
    // 3. 数据脱敏处理
    // 4. 分页数据包装
}
?>
```

### 练习 4：中间件性能分析器
开发中间件性能分析工具：

```php
<?php
// middleware_profiler.php

class MiddlewareProfiler {
    // 实现功能：
    // 1. 每个中间件的执行时间统计
    // 2. 内存使用分析
    // 3. 中间件调用链可视化
    // 4. 性能瓶颈识别
    // 5. 优化建议生成
}
?>
```

## 本章小结

本章深入介绍了 Swoole 中间件机制的设计和实现，从基础概念到高级应用，涵盖了现代 Web 应用中间件系统的各个方面。

**关键要点：**

- **洋葱模型**：理解中间件的执行流程和数据流向
- **常用中间件**：认证、限流、缓存、压缩等实用中间件
- **异常处理**：完善的错误处理和异常传播机制
- **性能优化**：中间件的性能考虑和优化策略
- **可扩展性**：模块化设计和插件化架构

**设计原则：**

1. **单一职责**：每个中间件只负责一个特定功能
2. **链式调用**：支持中间件的组合和嵌套
3. **异常安全**：完善的异常处理和恢复机制
4. **性能优先**：避免不必要的计算和内存分配
5. **可测试性**：中间件应该易于单元测试

**最佳实践：**

1. 合理安排中间件执行顺序
2. 避免在中间件中执行耗时操作
3. 使用适当的缓存策略
4. 实现完善的日志和监控
5. 提供清晰的错误信息

**下一章预告：**
下一章我们将学习静态文件服务，了解如何高效地处理静态资源，包括文件缓存、压缩、CDN 集成等内容。
