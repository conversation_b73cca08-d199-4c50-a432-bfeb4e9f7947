# 第12章：实时通信应用

## 学习目标
- 掌握实时聊天系统的完整实现
- 学会构建协作工具和文档编辑器
- 理解实时数据同步机制
- 掌握多人在线游戏的基础架构

## 12.1 实时聊天系统

### 12.1.1 完整聊天室实现

```php
<?php
// chat_system.php

use Swoole\WebSocket\Server;
use Swoole\Table;
use Swoole\Timer;

class ChatSystem {
    private $server;
    private $users;
    private $rooms;
    private $messages;
    private $onlineUsers;
    
    public function __construct($host = '0.0.0.0', $port = 9501) {
        $this->server = new Server($host, $port);
        $this->initializeTables();
        $this->setupEvents();
        $this->setupTimers();
    }
    
    private function initializeTables() {
        // 用户表
        $this->users = new Table(10000);
        $this->users->column('fd', Table::TYPE_INT);
        $this->users->column('username', Table::TYPE_STRING, 64);
        $this->users->column('avatar', Table::TYPE_STRING, 255);
        $this->users->column('room_id', Table::TYPE_STRING, 64);
        $this->users->column('join_time', Table::TYPE_INT);
        $this->users->column('last_active', Table::TYPE_INT);
        $this->users->column('status', Table::TYPE_STRING, 16);
        $this->users->create();
        
        // 房间表
        $this->rooms = new Table(1000);
        $this->rooms->column('name', Table::TYPE_STRING, 128);
        $this->rooms->column('description', Table::TYPE_STRING, 255);
        $this->rooms->column('created_by', Table::TYPE_STRING, 64);
        $this->rooms->column('created_at', Table::TYPE_INT);
        $this->rooms->column('user_count', Table::TYPE_INT);
        $this->rooms->column('max_users', Table::TYPE_INT);
        $this->rooms->column('is_private', Table::TYPE_INT);
        $this->rooms->create();
        
        // 消息历史表
        $this->messages = new Table(50000);
        $this->messages->column('id', Table::TYPE_STRING, 64);
        $this->messages->column('room_id', Table::TYPE_STRING, 64);
        $this->messages->column('username', Table::TYPE_STRING, 64);
        $this->messages->column('content', Table::TYPE_STRING, 1000);
        $this->messages->column('type', Table::TYPE_STRING, 16);
        $this->messages->column('timestamp', Table::TYPE_INT);
        $this->messages->create();
        
        // 在线用户表
        $this->onlineUsers = new Table(10000);
        $this->onlineUsers->column('username', Table::TYPE_STRING, 64);
        $this->onlineUsers->column('room_id', Table::TYPE_STRING, 64);
        $this->onlineUsers->column('last_seen', Table::TYPE_INT);
        $this->onlineUsers->create();
        
        // 创建默认房间
        $this->createDefaultRooms();
    }
    
    private function createDefaultRooms() {
        $defaultRooms = [
            'general' => ['name' => '大厅', 'description' => '欢迎来到聊天大厅'],
            'tech' => ['name' => '技术讨论', 'description' => '技术相关话题讨论'],
            'random' => ['name' => '随便聊聊', 'description' => '随意聊天的地方'],
        ];
        
        foreach ($defaultRooms as $id => $room) {
            $this->rooms->set($id, [
                'name' => $room['name'],
                'description' => $room['description'],
                'created_by' => 'system',
                'created_at' => time(),
                'user_count' => 0,
                'max_users' => 100,
                'is_private' => 0
            ]);
        }
    }
    
    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('request', [$this, 'onRequest']);
    }
    
    private function setupTimers() {
        // 定期清理离线用户
        Timer::tick(30000, function() {
            $this->cleanupOfflineUsers();
        });
        
        // 定期清理旧消息
        Timer::tick(300000, function() {
            $this->cleanupOldMessages();
        });
    }
    
    public function onOpen($server, $request) {
        echo "新连接: FD={$request->fd}\n";
        
        // 发送欢迎消息和房间列表
        $this->sendWelcomeMessage($request->fd);
        $this->sendRoomList($request->fd);
    }
    
    public function onMessage($server, $frame) {
        $data = json_decode($frame->data, true);
        
        if (!$data || !isset($data['type'])) {
            $this->sendError($frame->fd, '无效的消息格式');
            return;
        }
        
        switch ($data['type']) {
            case 'join':
                $this->handleJoin($frame->fd, $data);
                break;
            case 'message':
                $this->handleMessage($frame->fd, $data);
                break;
            case 'private_message':
                $this->handlePrivateMessage($frame->fd, $data);
                break;
            case 'change_room':
                $this->handleChangeRoom($frame->fd, $data);
                break;
            case 'get_history':
                $this->handleGetHistory($frame->fd, $data);
                break;
            case 'get_online_users':
                $this->handleGetOnlineUsers($frame->fd, $data);
                break;
            case 'typing':
                $this->handleTyping($frame->fd, $data);
                break;
            default:
                $this->sendError($frame->fd, '未知的消息类型');
                break;
        }
    }
    
    public function onClose($server, $fd) {
        $user = $this->getUserByFd($fd);
        if ($user) {
            $this->handleUserLeave($fd, $user);
        }
        echo "连接关闭: FD={$fd}\n";
    }
    
    public function onRequest($request, $response) {
        $uri = $request->server['request_uri'];
        
        switch ($uri) {
            case '/':
                $this->serveChatPage($response);
                break;
            case '/api/stats':
                $this->serveStats($response);
                break;
            case '/api/rooms':
                $this->serveRooms($response);
                break;
            default:
                $response->status(404);
                $response->end('Not Found');
                break;
        }
    }
    
    private function handleJoin($fd, $data) {
        $username = trim($data['username'] ?? '');
        $roomId = $data['room_id'] ?? 'general';
        $avatar = $data['avatar'] ?? '';
        
        if (empty($username)) {
            $this->sendError($fd, '用户名不能为空');
            return;
        }
        
        // 检查用户名是否已存在
        if ($this->isUsernameExists($username)) {
            $this->sendError($fd, '用户名已存在，请选择其他用户名');
            return;
        }
        
        // 检查房间是否存在
        if (!$this->rooms->exist($roomId)) {
            $this->sendError($fd, '房间不存在');
            return;
        }
        
        // 检查房间人数限制
        $room = $this->rooms->get($roomId);
        if ($room['user_count'] >= $room['max_users']) {
            $this->sendError($fd, '房间人数已满');
            return;
        }
        
        // 添加用户
        $now = time();
        $this->users->set($fd, [
            'fd' => $fd,
            'username' => $username,
            'avatar' => $avatar,
            'room_id' => $roomId,
            'join_time' => $now,
            'last_active' => $now,
            'status' => 'online'
        ]);
        
        // 更新在线用户表
        $this->onlineUsers->set($username, [
            'username' => $username,
            'room_id' => $roomId,
            'last_seen' => $now
        ]);
        
        // 更新房间用户数
        $room['user_count']++;
        $this->rooms->set($roomId, $room);
        
        // 发送加入成功消息
        $this->server->push($fd, json_encode([
            'type' => 'join_success',
            'username' => $username,
            'room_id' => $roomId,
            'room_name' => $room['name']
        ]));
        
        // 广播用户加入消息
        $this->broadcastToRoom($roomId, [
            'type' => 'user_joined',
            'username' => $username,
            'avatar' => $avatar,
            'message' => "{$username} 加入了聊天室",
            'timestamp' => $now
        ], $fd);
        
        // 发送最近消息历史
        $this->sendRecentMessages($fd, $roomId);
        
        // 发送在线用户列表
        $this->sendOnlineUsers($fd, $roomId);
        
        echo "用户加入: {$username} -> {$roomId}\n";
    }
    
    private function handleMessage($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入聊天室');
            return;
        }
        
        $content = trim($data['content'] ?? '');
        if (empty($content)) {
            $this->sendError($fd, '消息内容不能为空');
            return;
        }
        
        // 更新用户活跃时间
        $user['last_active'] = time();
        $this->users->set($fd, $user);
        
        // 保存消息到历史
        $messageId = uniqid();
        $timestamp = time();
        
        $this->messages->set($messageId, [
            'id' => $messageId,
            'room_id' => $user['room_id'],
            'username' => $user['username'],
            'content' => $content,
            'type' => 'message',
            'timestamp' => $timestamp
        ]);
        
        // 广播消息
        $message = [
            'type' => 'message',
            'id' => $messageId,
            'username' => $user['username'],
            'avatar' => $user['avatar'],
            'content' => $content,
            'timestamp' => $timestamp
        ];
        
        $this->broadcastToRoom($user['room_id'], $message);
        
        echo "消息: {$user['username']} -> {$content}\n";
    }
    
    private function handlePrivateMessage($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入聊天室');
            return;
        }
        
        $targetUsername = $data['target'] ?? '';
        $content = trim($data['content'] ?? '');
        
        if (empty($targetUsername) || empty($content)) {
            $this->sendError($fd, '目标用户和消息内容不能为空');
            return;
        }
        
        // 查找目标用户
        $targetUser = $this->getUserByUsername($targetUsername);
        if (!$targetUser) {
            $this->sendError($fd, '目标用户不在线');
            return;
        }
        
        $timestamp = time();
        
        // 发送给目标用户
        $this->server->push($targetUser['fd'], json_encode([
            'type' => 'private_message',
            'from' => $user['username'],
            'avatar' => $user['avatar'],
            'content' => $content,
            'timestamp' => $timestamp
        ]));
        
        // 发送确认给发送者
        $this->server->push($fd, json_encode([
            'type' => 'private_message_sent',
            'to' => $targetUsername,
            'content' => $content,
            'timestamp' => $timestamp
        ]));
        
        echo "私信: {$user['username']} -> {$targetUsername}: {$content}\n";
    }
    
    private function handleChangeRoom($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入聊天室');
            return;
        }
        
        $newRoomId = $data['room_id'] ?? '';
        if (empty($newRoomId) || !$this->rooms->exist($newRoomId)) {
            $this->sendError($fd, '房间不存在');
            return;
        }
        
        if ($user['room_id'] === $newRoomId) {
            $this->sendError($fd, '您已经在该房间中');
            return;
        }
        
        $oldRoomId = $user['room_id'];
        
        // 从旧房间离开
        $this->leaveRoom($fd, $user, $oldRoomId);
        
        // 加入新房间
        $this->joinRoom($fd, $user, $newRoomId);
    }
    
    private function handleGetHistory($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入聊天室');
            return;
        }
        
        $roomId = $data['room_id'] ?? $user['room_id'];
        $limit = min($data['limit'] ?? 50, 100);
        
        $messages = $this->getRecentMessages($roomId, $limit);
        
        $this->server->push($fd, json_encode([
            'type' => 'message_history',
            'room_id' => $roomId,
            'messages' => $messages
        ]));
    }
    
    private function handleGetOnlineUsers($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入聊天室');
            return;
        }
        
        $roomId = $data['room_id'] ?? $user['room_id'];
        $this->sendOnlineUsers($fd, $roomId);
    }
    
    private function handleTyping($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            return;
        }
        
        $isTyping = $data['typing'] ?? false;
        
        // 广播打字状态
        $this->broadcastToRoom($user['room_id'], [
            'type' => 'typing',
            'username' => $user['username'],
            'typing' => $isTyping
        ], $fd);
    }
    
    private function handleUserLeave($fd, $user) {
        $roomId = $user['room_id'];
        
        // 从房间移除用户
        $room = $this->rooms->get($roomId);
        if ($room) {
            $room['user_count']--;
            $this->rooms->set($roomId, $room);
        }
        
        // 从用户表移除
        $this->users->del($fd);
        $this->onlineUsers->del($user['username']);
        
        // 广播用户离开消息
        $this->broadcastToRoom($roomId, [
            'type' => 'user_left',
            'username' => $user['username'],
            'message' => "{$user['username']} 离开了聊天室",
            'timestamp' => time()
        ]);
        
        echo "用户离开: {$user['username']}\n";
    }
    
    private function leaveRoom($fd, $user, $roomId) {
        // 更新房间用户数
        $room = $this->rooms->get($roomId);
        if ($room) {
            $room['user_count']--;
            $this->rooms->set($roomId, $room);
        }
        
        // 广播离开消息
        $this->broadcastToRoom($roomId, [
            'type' => 'user_left',
            'username' => $user['username'],
            'message' => "{$user['username']} 离开了 {$room['name']}",
            'timestamp' => time()
        ], $fd);
    }
    
    private function joinRoom($fd, $user, $roomId) {
        // 更新用户房间
        $user['room_id'] = $roomId;
        $this->users->set($fd, $user);
        
        // 更新在线用户表
        $onlineUser = $this->onlineUsers->get($user['username']);
        $onlineUser['room_id'] = $roomId;
        $this->onlineUsers->set($user['username'], $onlineUser);
        
        // 更新房间用户数
        $room = $this->rooms->get($roomId);
        $room['user_count']++;
        $this->rooms->set($roomId, $room);
        
        // 发送房间切换成功消息
        $this->server->push($fd, json_encode([
            'type' => 'room_changed',
            'room_id' => $roomId,
            'room_name' => $room['name']
        ]));
        
        // 广播加入消息
        $this->broadcastToRoom($roomId, [
            'type' => 'user_joined',
            'username' => $user['username'],
            'avatar' => $user['avatar'],
            'message' => "{$user['username']} 加入了 {$room['name']}",
            'timestamp' => time()
        ], $fd);
        
        // 发送最近消息和在线用户
        $this->sendRecentMessages($fd, $roomId);
        $this->sendOnlineUsers($fd, $roomId);
    }
    
    // 辅助方法
    private function sendWelcomeMessage($fd) {
        $this->server->push($fd, json_encode([
            'type' => 'welcome',
            'message' => '欢迎来到实时聊天系统！',
            'server_time' => date('Y-m-d H:i:s')
        ]));
    }
    
    private function sendRoomList($fd) {
        $rooms = [];
        foreach ($this->rooms as $id => $room) {
            $rooms[] = [
                'id' => $id,
                'name' => $room['name'],
                'description' => $room['description'],
                'user_count' => $room['user_count'],
                'max_users' => $room['max_users']
            ];
        }
        
        $this->server->push($fd, json_encode([
            'type' => 'room_list',
            'rooms' => $rooms
        ]));
    }
    
    private function sendError($fd, $message) {
        if ($this->server->isEstablished($fd)) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => $message
            ]));
        }
    }
    
    private function broadcastToRoom($roomId, $message, $excludeFd = null) {
        foreach ($this->users as $fd => $user) {
            if ($user['room_id'] === $roomId && $fd !== $excludeFd) {
                if ($this->server->isEstablished($fd)) {
                    $this->server->push($fd, json_encode($message));
                }
            }
        }
    }
    
    private function sendRecentMessages($fd, $roomId, $limit = 20) {
        $messages = $this->getRecentMessages($roomId, $limit);
        
        $this->server->push($fd, json_encode([
            'type' => 'message_history',
            'messages' => $messages
        ]));
    }
    
    private function getRecentMessages($roomId, $limit = 20) {
        $messages = [];
        $count = 0;
        
        foreach ($this->messages as $id => $message) {
            if ($message['room_id'] === $roomId) {
                $messages[] = $message;
                $count++;
                if ($count >= $limit) {
                    break;
                }
            }
        }
        
        // 按时间排序
        usort($messages, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });
        
        return array_slice($messages, 0, $limit);
    }
    
    private function sendOnlineUsers($fd, $roomId) {
        $users = [];
        
        foreach ($this->users as $userFd => $user) {
            if ($user['room_id'] === $roomId) {
                $users[] = [
                    'username' => $user['username'],
                    'avatar' => $user['avatar'],
                    'status' => $user['status'],
                    'join_time' => $user['join_time']
                ];
            }
        }
        
        $this->server->push($fd, json_encode([
            'type' => 'online_users',
            'room_id' => $roomId,
            'users' => $users
        ]));
    }
    
    private function getUserByFd($fd) {
        return $this->users->exist($fd) ? $this->users->get($fd) : null;
    }
    
    private function getUserByUsername($username) {
        foreach ($this->users as $fd => $user) {
            if ($user['username'] === $username) {
                return $user;
            }
        }
        return null;
    }
    
    private function isUsernameExists($username) {
        return $this->onlineUsers->exist($username);
    }
    
    private function cleanupOfflineUsers() {
        $now = time();
        $timeout = 300; // 5分钟
        
        foreach ($this->users as $fd => $user) {
            if (($now - $user['last_active']) > $timeout) {
                if (!$this->server->isEstablished($fd)) {
                    $this->handleUserLeave($fd, $user);
                }
            }
        }
    }
    
    private function cleanupOldMessages() {
        $now = time();
        $maxAge = 86400; // 24小时
        
        foreach ($this->messages as $id => $message) {
            if (($now - $message['timestamp']) > $maxAge) {
                $this->messages->del($id);
            }
        }
    }
    
    private function serveChatPage($response) {
        // 这里应该返回聊天页面的 HTML
        $response->header('Content-Type', 'text/html; charset=utf-8');
        $response->end('<h1>聊天系统</h1><p>请使用 WebSocket 客户端连接</p>');
    }
    
    private function serveStats($response) {
        $stats = [
            'total_users' => count($this->users),
            'total_rooms' => count($this->rooms),
            'total_messages' => count($this->messages),
            'uptime' => time() - $this->startTime ?? time()
        ];
        
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($stats, JSON_PRETTY_PRINT));
    }
    
    private function serveRooms($response) {
        $rooms = [];
        foreach ($this->rooms as $id => $room) {
            $rooms[$id] = $room;
        }
        
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($rooms, JSON_PRETTY_PRINT));
    }
    
    public function start() {
        $this->startTime = time();
        echo "聊天系统启动成功，监听端口 9501\n";
        $this->server->start();
    }
}

$chatSystem = new ChatSystem('0.0.0.0', 9501);
$chatSystem->start();
?>
```

## 12.2 协作文档编辑器

### 12.2.1 实时文档同步

```php
<?php
// collaborative_editor.php

use Swoole\WebSocket\Server;
use Swoole\Table;

class CollaborativeEditor {
    private $server;
    private $documents;
    private $users;
    private $operations;
    private $cursors;

    public function __construct($host = '0.0.0.0', $port = 9502) {
        $this->server = new Server($host, $port);
        $this->initializeTables();
        $this->setupEvents();
    }

    private function initializeTables() {
        // 文档表
        $this->documents = new Table(1000);
        $this->documents->column('id', Table::TYPE_STRING, 64);
        $this->documents->column('title', Table::TYPE_STRING, 255);
        $this->documents->column('content', Table::TYPE_STRING, 65536);
        $this->documents->column('version', Table::TYPE_INT);
        $this->documents->column('created_at', Table::TYPE_INT);
        $this->documents->column('updated_at', Table::TYPE_INT);
        $this->documents->create();

        // 用户表
        $this->users = new Table(10000);
        $this->users->column('fd', Table::TYPE_INT);
        $this->users->column('username', Table::TYPE_STRING, 64);
        $this->users->column('document_id', Table::TYPE_STRING, 64);
        $this->users->column('color', Table::TYPE_STRING, 16);
        $this->users->column('join_time', Table::TYPE_INT);
        $this->users->column('last_active', Table::TYPE_INT);
        $this->users->create();

        // 操作历史表
        $this->operations = new Table(100000);
        $this->operations->column('id', Table::TYPE_STRING, 64);
        $this->operations->column('document_id', Table::TYPE_STRING, 64);
        $this->operations->column('username', Table::TYPE_STRING, 64);
        $this->operations->column('type', Table::TYPE_STRING, 16);
        $this->operations->column('position', Table::TYPE_INT);
        $this->operations->column('content', Table::TYPE_STRING, 1000);
        $this->operations->column('version', Table::TYPE_INT);
        $this->operations->column('timestamp', Table::TYPE_INT);
        $this->operations->create();

        // 光标位置表
        $this->cursors = new Table(10000);
        $this->cursors->column('username', Table::TYPE_STRING, 64);
        $this->cursors->column('document_id', Table::TYPE_STRING, 64);
        $this->cursors->column('position', Table::TYPE_INT);
        $this->cursors->column('selection_start', Table::TYPE_INT);
        $this->cursors->column('selection_end', Table::TYPE_INT);
        $this->cursors->column('updated_at', Table::TYPE_INT);
        $this->cursors->create();

        // 创建示例文档
        $this->createSampleDocument();
    }

    private function createSampleDocument() {
        $docId = 'sample-doc';
        $this->documents->set($docId, [
            'id' => $docId,
            'title' => '示例文档',
            'content' => "# 欢迎使用协作编辑器\n\n这是一个实时协作文档编辑器的示例。\n\n## 功能特性\n\n- 实时同步编辑\n- 多用户协作\n- 操作历史记录\n- 光标位置同步\n\n开始编辑吧！",
            'version' => 1,
            'created_at' => time(),
            'updated_at' => time()
        ]);
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('request', [$this, 'onRequest']);
    }

    public function onOpen($server, $request) {
        echo "新连接: FD={$request->fd}\n";

        $this->server->push($request->fd, json_encode([
            'type' => 'welcome',
            'message' => '欢迎使用协作编辑器'
        ]));
    }

    public function onMessage($server, $frame) {
        $data = json_decode($frame->data, true);

        if (!$data || !isset($data['type'])) {
            $this->sendError($frame->fd, '无效的消息格式');
            return;
        }

        switch ($data['type']) {
            case 'join_document':
                $this->handleJoinDocument($frame->fd, $data);
                break;
            case 'text_operation':
                $this->handleTextOperation($frame->fd, $data);
                break;
            case 'cursor_update':
                $this->handleCursorUpdate($frame->fd, $data);
                break;
            case 'get_document':
                $this->handleGetDocument($frame->fd, $data);
                break;
            case 'get_history':
                $this->handleGetHistory($frame->fd, $data);
                break;
            default:
                $this->sendError($frame->fd, '未知的消息类型');
                break;
        }
    }

    public function onClose($server, $fd) {
        $user = $this->users->exist($fd) ? $this->users->get($fd) : null;
        if ($user) {
            $this->handleUserLeave($fd, $user);
        }
        echo "连接关闭: FD={$fd}\n";
    }

    public function onRequest($request, $response) {
        $uri = $request->server['request_uri'];

        switch ($uri) {
            case '/':
                $this->serveEditorPage($response);
                break;
            case '/api/documents':
                $this->serveDocuments($response);
                break;
            default:
                $response->status(404);
                $response->end('Not Found');
                break;
        }
    }

    private function handleJoinDocument($fd, $data) {
        $username = trim($data['username'] ?? '');
        $documentId = $data['document_id'] ?? '';

        if (empty($username) || empty($documentId)) {
            $this->sendError($fd, '用户名和文档ID不能为空');
            return;
        }

        if (!$this->documents->exist($documentId)) {
            $this->sendError($fd, '文档不存在');
            return;
        }

        // 生成用户颜色
        $colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
        $color = $colors[array_rand($colors)];

        // 添加用户
        $this->users->set($fd, [
            'fd' => $fd,
            'username' => $username,
            'document_id' => $documentId,
            'color' => $color,
            'join_time' => time(),
            'last_active' => time()
        ]);

        // 初始化光标位置
        $this->cursors->set($username, [
            'username' => $username,
            'document_id' => $documentId,
            'position' => 0,
            'selection_start' => 0,
            'selection_end' => 0,
            'updated_at' => time()
        ]);

        // 发送文档内容
        $document = $this->documents->get($documentId);
        $this->server->push($fd, json_encode([
            'type' => 'document_joined',
            'document' => $document,
            'user_color' => $color
        ]));

        // 发送当前在线用户列表
        $this->sendOnlineUsers($fd, $documentId);

        // 广播用户加入消息
        $this->broadcastToDocument($documentId, [
            'type' => 'user_joined',
            'username' => $username,
            'color' => $color
        ], $fd);

        echo "用户加入文档: {$username} -> {$documentId}\n";
    }

    private function handleTextOperation($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入文档');
            return;
        }

        $operation = $data['operation'] ?? [];
        $version = $data['version'] ?? 0;

        if (empty($operation)) {
            $this->sendError($fd, '操作不能为空');
            return;
        }

        $documentId = $user['document_id'];
        $document = $this->documents->get($documentId);

        // 版本检查
        if ($version !== $document['version']) {
            $this->sendError($fd, '文档版本不匹配，请刷新');
            return;
        }

        // 应用操作
        $newContent = $this->applyOperation($document['content'], $operation);
        $newVersion = $document['version'] + 1;

        // 更新文档
        $document['content'] = $newContent;
        $document['version'] = $newVersion;
        $document['updated_at'] = time();
        $this->documents->set($documentId, $document);

        // 记录操作历史
        $operationId = uniqid();
        $this->operations->set($operationId, [
            'id' => $operationId,
            'document_id' => $documentId,
            'username' => $user['username'],
            'type' => $operation['type'],
            'position' => $operation['position'] ?? 0,
            'content' => $operation['content'] ?? '',
            'version' => $newVersion,
            'timestamp' => time()
        ]);

        // 广播操作给其他用户
        $this->broadcastToDocument($documentId, [
            'type' => 'text_operation',
            'operation' => $operation,
            'version' => $newVersion,
            'username' => $user['username']
        ], $fd);

        // 发送确认给操作者
        $this->server->push($fd, json_encode([
            'type' => 'operation_applied',
            'version' => $newVersion
        ]));

        echo "文本操作: {$user['username']} -> {$operation['type']}\n";
    }

    private function handleCursorUpdate($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            return;
        }

        $position = $data['position'] ?? 0;
        $selectionStart = $data['selection_start'] ?? $position;
        $selectionEnd = $data['selection_end'] ?? $position;

        // 更新光标位置
        $this->cursors->set($user['username'], [
            'username' => $user['username'],
            'document_id' => $user['document_id'],
            'position' => $position,
            'selection_start' => $selectionStart,
            'selection_end' => $selectionEnd,
            'updated_at' => time()
        ]);

        // 广播光标位置
        $this->broadcastToDocument($user['document_id'], [
            'type' => 'cursor_update',
            'username' => $user['username'],
            'color' => $user['color'],
            'position' => $position,
            'selection_start' => $selectionStart,
            'selection_end' => $selectionEnd
        ], $fd);
    }

    private function handleGetDocument($fd, $data) {
        $documentId = $data['document_id'] ?? '';

        if (!$this->documents->exist($documentId)) {
            $this->sendError($fd, '文档不存在');
            return;
        }

        $document = $this->documents->get($documentId);

        $this->server->push($fd, json_encode([
            'type' => 'document_content',
            'document' => $document
        ]));
    }

    private function handleGetHistory($fd, $data) {
        $user = $this->users->get($fd);
        if (!$user) {
            $this->sendError($fd, '请先加入文档');
            return;
        }

        $limit = min($data['limit'] ?? 50, 100);
        $history = $this->getOperationHistory($user['document_id'], $limit);

        $this->server->push($fd, json_encode([
            'type' => 'operation_history',
            'history' => $history
        ]));
    }

    private function handleUserLeave($fd, $user) {
        $documentId = $user['document_id'];

        // 移除用户和光标
        $this->users->del($fd);
        $this->cursors->del($user['username']);

        // 广播用户离开消息
        $this->broadcastToDocument($documentId, [
            'type' => 'user_left',
            'username' => $user['username']
        ]);

        echo "用户离开文档: {$user['username']}\n";
    }

    private function applyOperation($content, $operation) {
        switch ($operation['type']) {
            case 'insert':
                $position = $operation['position'];
                $text = $operation['content'];
                return substr($content, 0, $position) . $text . substr($content, $position);

            case 'delete':
                $position = $operation['position'];
                $length = $operation['length'] ?? 1;
                return substr($content, 0, $position) . substr($content, $position + $length);

            case 'replace':
                $position = $operation['position'];
                $length = $operation['length'] ?? 0;
                $text = $operation['content'];
                return substr($content, 0, $position) . $text . substr($content, $position + $length);

            default:
                return $content;
        }
    }

    private function sendError($fd, $message) {
        if ($this->server->isEstablished($fd)) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => $message
            ]));
        }
    }

    private function broadcastToDocument($documentId, $message, $excludeFd = null) {
        foreach ($this->users as $fd => $user) {
            if ($user['document_id'] === $documentId && $fd !== $excludeFd) {
                if ($this->server->isEstablished($fd)) {
                    $this->server->push($fd, json_encode($message));
                }
            }
        }
    }

    private function sendOnlineUsers($fd, $documentId) {
        $users = [];

        foreach ($this->users as $userFd => $user) {
            if ($user['document_id'] === $documentId) {
                $cursor = $this->cursors->get($user['username']);
                $users[] = [
                    'username' => $user['username'],
                    'color' => $user['color'],
                    'cursor_position' => $cursor ? $cursor['position'] : 0
                ];
            }
        }

        $this->server->push($fd, json_encode([
            'type' => 'online_users',
            'users' => $users
        ]));
    }

    private function getOperationHistory($documentId, $limit) {
        $history = [];
        $count = 0;

        foreach ($this->operations as $id => $operation) {
            if ($operation['document_id'] === $documentId) {
                $history[] = $operation;
                $count++;
                if ($count >= $limit) {
                    break;
                }
            }
        }

        // 按时间排序
        usort($history, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });

        return array_slice($history, 0, $limit);
    }

    private function serveEditorPage($response) {
        $html = '
<!DOCTYPE html>
<html>
<head>
    <title>协作文档编辑器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .editor { border: 1px solid #ccc; min-height: 500px; padding: 20px; font-family: monospace; }
        .users { display: flex; gap: 10px; margin-bottom: 10px; }
        .user { padding: 5px 10px; border-radius: 15px; color: white; font-size: 12px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>协作文档编辑器</h1>
            <div>
                <input type="text" id="username" placeholder="输入用户名" value="User' . rand(1, 1000) . '">
                <button id="join">加入编辑</button>
                <button id="disconnect" disabled>断开连接</button>
            </div>
        </div>

        <div id="status" class="status disconnected">未连接</div>

        <div class="users" id="users"></div>

        <textarea id="editor" class="editor" placeholder="连接后开始编辑..." disabled></textarea>

        <div style="margin-top: 10px;">
            <button id="history">查看历史</button>
            <span id="version">版本: 0</span>
        </div>
    </div>

    <script>
        let ws = null;
        let currentVersion = 0;
        let isConnected = false;
        let username = "";

        const editor = document.getElementById("editor");
        const status = document.getElementById("status");
        const users = document.getElementById("users");
        const version = document.getElementById("version");

        function updateStatus(connected) {
            status.textContent = connected ? "已连接" : "未连接";
            status.className = `status ${connected ? "connected" : "disconnected"}`;
            document.getElementById("join").disabled = connected;
            document.getElementById("disconnect").disabled = !connected;
            editor.disabled = !connected;
            isConnected = connected;
        }

        function updateUsers(userList) {
            users.innerHTML = "";
            userList.forEach(user => {
                const userDiv = document.createElement("div");
                userDiv.className = "user";
                userDiv.style.backgroundColor = user.color;
                userDiv.textContent = user.username;
                users.appendChild(userDiv);
            });
        }

        document.getElementById("join").onclick = () => {
            username = document.getElementById("username").value.trim();
            if (!username) {
                alert("请输入用户名");
                return;
            }

            ws = new WebSocket(`ws://${location.host}`);

            ws.onopen = () => {
                updateStatus(true);
                ws.send(JSON.stringify({
                    type: "join_document",
                    username: username,
                    document_id: "sample-doc"
                }));
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                handleMessage(data);
            };

            ws.onclose = () => {
                updateStatus(false);
            };
        };

        document.getElementById("disconnect").onclick = () => {
            if (ws) ws.close();
        };

        function handleMessage(data) {
            switch (data.type) {
                case "document_joined":
                    editor.value = data.document.content;
                    currentVersion = data.document.version;
                    version.textContent = `版本: ${currentVersion}`;
                    break;

                case "online_users":
                    updateUsers(data.users);
                    break;

                case "text_operation":
                    if (data.username !== username) {
                        applyOperation(data.operation);
                        currentVersion = data.version;
                        version.textContent = `版本: ${currentVersion}`;
                    }
                    break;

                case "operation_applied":
                    currentVersion = data.version;
                    version.textContent = `版本: ${currentVersion}`;
                    break;

                case "user_joined":
                    console.log(`${data.username} 加入了文档`);
                    break;

                case "user_left":
                    console.log(`${data.username} 离开了文档`);
                    break;

                case "error":
                    alert("错误: " + data.message);
                    break;
            }
        }

        function applyOperation(operation) {
            const content = editor.value;
            switch (operation.type) {
                case "insert":
                    editor.value = content.slice(0, operation.position) +
                                  operation.content +
                                  content.slice(operation.position);
                    break;
                case "delete":
                    editor.value = content.slice(0, operation.position) +
                                  content.slice(operation.position + operation.length);
                    break;
            }
        }

        let lastContent = "";
        let lastCursorPos = 0;

        editor.oninput = () => {
            if (!isConnected) return;

            const content = editor.value;
            const cursorPos = editor.selectionStart;

            if (content !== lastContent) {
                const operation = detectOperation(lastContent, content, lastCursorPos);
                if (operation && ws) {
                    ws.send(JSON.stringify({
                        type: "text_operation",
                        operation: operation,
                        version: currentVersion
                    }));
                }
                lastContent = content;
            }
            lastCursorPos = cursorPos;
        };

        function detectOperation(oldContent, newContent, cursorPos) {
            if (newContent.length > oldContent.length) {
                // 插入操作
                const insertedText = newContent.slice(cursorPos - (newContent.length - oldContent.length), cursorPos);
                return {
                    type: "insert",
                    position: cursorPos - insertedText.length,
                    content: insertedText
                };
            } else if (newContent.length < oldContent.length) {
                // 删除操作
                return {
                    type: "delete",
                    position: cursorPos,
                    length: oldContent.length - newContent.length
                };
            }
            return null;
        }

        document.getElementById("history").onclick = () => {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: "get_history",
                    limit: 20
                }));
            }
        };
    </script>
</body>
</html>';

        $response->header('Content-Type', 'text/html; charset=utf-8');
        $response->end($html);
    }

    private function serveDocuments($response) {
        $docs = [];
        foreach ($this->documents as $id => $doc) {
            $docs[$id] = $doc;
        }

        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($docs, JSON_PRETTY_PRINT));
    }

    public function start() {
        echo "协作编辑器启动成功，监听端口 9502\n";
        $this->server->start();
    }
}

$editor = new CollaborativeEditor('0.0.0.0', 9502);
$editor->start();
?>
