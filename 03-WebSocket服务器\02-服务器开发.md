# 第11章：WebSocket 服务器开发

## 学习目标
- 掌握 WebSocket 服务器的完整开发流程
- 学会连接生命周期管理
- 理解消息路由和处理机制
- 掌握服务器配置和性能优化

## 11.1 WebSocket 服务器架构

### 11.1.1 服务器基础架构

```php
<?php
// websocket_server_base.php

use Swoole\WebSocket\Server;
use Swoole\Http\Request;
use Swoole\WebSocket\Frame;
use Swoole\Timer;

class WebSocketServerBase {
    protected $server;
    protected $config;
    protected $connections = [];
    protected $stats = [
        'total_connections' => 0,
        'current_connections' => 0,
        'messages_sent' => 0,
        'messages_received' => 0,
        'start_time' => 0
    ];
    
    public function __construct($host = '0.0.0.0', $port = 9501, $config = []) {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->server = new Server($host, $port);
        $this->setupServer();
        $this->setupEvents();
        $this->setupTimers();
    }
    
    protected function getDefaultConfig() {
        return [
            'worker_num' => swoole_cpu_num(),
            'max_conn' => 10000,
            'heartbeat_check_interval' => 60,
            'heartbeat_idle_time' => 600,
            'max_request' => 0,
            'task_worker_num' => 2,
            'enable_static_handler' => true,
            'document_root' => __DIR__ . '/public',
            'log_level' => SWOOLE_LOG_INFO,
            'daemonize' => false,
        ];
    }
    
    protected function setupServer() {
        $this->server->set($this->config);
    }
    
    protected function setupEvents() {
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('request', [$this, 'onRequest']);
        $this->server->on('task', [$this, 'onTask']);
        $this->server->on('finish', [$this, 'onFinish']);
    }
    
    protected function setupTimers() {
        // 在 Worker 进程启动后设置定时器
    }
    
    public function onStart($server) {
        $this->stats['start_time'] = time();
        echo "WebSocket 服务器启动成功\n";
        echo "监听地址: ws://{$server->host}:{$server->port}\n";
        echo "Master PID: {$server->master_pid}\n";
        echo "Manager PID: {$server->manager_pid}\n";
    }
    
    public function onWorkerStart($server, $workerId) {
        echo "Worker #{$workerId} 启动，PID: " . getmypid() . "\n";
        
        // 只在第一个 Worker 进程中设置定时器
        if ($workerId === 0) {
            $this->setupWorkerTimers();
        }
    }
    
    protected function setupWorkerTimers() {
        // 统计信息定时器
        Timer::tick(30000, function() {
            $this->logStats();
        });
        
        // 连接清理定时器
        Timer::tick(60000, function() {
            $this->cleanupConnections();
        });
    }
    
    public function onOpen($server, $request) {
        $this->stats['total_connections']++;
        $this->stats['current_connections']++;
        
        // 存储连接信息
        $this->connections[$request->fd] = [
            'fd' => $request->fd,
            'ip' => $request->server['remote_addr'],
            'port' => $request->server['remote_port'],
            'connect_time' => time(),
            'last_message_time' => time(),
            'message_count' => 0,
            'user_agent' => $request->header['user-agent'] ?? '',
            'origin' => $request->header['origin'] ?? '',
        ];
        
        echo "新连接: FD={$request->fd}, IP={$request->server['remote_addr']}\n";
        
        // 发送欢迎消息
        $this->sendWelcomeMessage($request->fd);
    }
    
    public function onMessage($server, $frame) {
        $this->stats['messages_received']++;
        
        // 更新连接信息
        if (isset($this->connections[$frame->fd])) {
            $this->connections[$frame->fd]['last_message_time'] = time();
            $this->connections[$frame->fd]['message_count']++;
        }
        
        echo "收到消息: FD={$frame->fd}, 长度=" . strlen($frame->data) . "\n";
        
        // 处理消息
        $this->handleMessage($server, $frame);
    }
    
    public function onClose($server, $fd) {
        $this->stats['current_connections']--;
        
        if (isset($this->connections[$fd])) {
            $connection = $this->connections[$fd];
            $duration = time() - $connection['connect_time'];
            
            echo "连接关闭: FD={$fd}, 持续时间={$duration}秒, 消息数={$connection['message_count']}\n";
            
            unset($this->connections[$fd]);
        }
        
        // 处理连接关闭
        $this->handleClose($server, $fd);
    }
    
    public function onRequest($request, $response) {
        $uri = $request->server['request_uri'];
        
        // 处理 HTTP 请求
        switch ($uri) {
            case '/':
                $this->serveHomePage($response);
                break;
            case '/stats':
                $this->serveStats($response);
                break;
            case '/connections':
                $this->serveConnections($response);
                break;
            default:
                $response->status(404);
                $response->end('Not Found');
                break;
        }
    }
    
    public function onTask($server, $taskId, $reactorId, $data) {
        echo "处理任务: TaskID={$taskId}, Data=" . json_encode($data) . "\n";
        
        // 处理异步任务
        $result = $this->handleTask($data);
        
        return $result;
    }
    
    public function onFinish($server, $taskId, $data) {
        echo "任务完成: TaskID={$taskId}, Result=" . json_encode($data) . "\n";
    }
    
    protected function sendWelcomeMessage($fd) {
        $message = [
            'type' => 'welcome',
            'message' => '欢迎连接到 WebSocket 服务器',
            'server_time' => date('Y-m-d H:i:s'),
            'fd' => $fd
        ];
        
        $this->sendMessage($fd, $message);
    }
    
    protected function handleMessage($server, $frame) {
        try {
            $data = json_decode($frame->data, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->sendError($frame->fd, 'Invalid JSON format');
                return;
            }
            
            $this->routeMessage($server, $frame->fd, $data);
            
        } catch (Exception $e) {
            echo "消息处理错误: " . $e->getMessage() . "\n";
            $this->sendError($frame->fd, 'Message processing error');
        }
    }
    
    protected function routeMessage($server, $fd, $data) {
        $type = $data['type'] ?? 'unknown';
        
        switch ($type) {
            case 'ping':
                $this->handlePing($fd, $data);
                break;
            case 'echo':
                $this->handleEcho($fd, $data);
                break;
            case 'broadcast':
                $this->handleBroadcast($fd, $data);
                break;
            case 'private':
                $this->handlePrivateMessage($fd, $data);
                break;
            default:
                $this->sendError($fd, "Unknown message type: {$type}");
                break;
        }
    }
    
    protected function handlePing($fd, $data) {
        $this->sendMessage($fd, [
            'type' => 'pong',
            'timestamp' => microtime(true)
        ]);
    }
    
    protected function handleEcho($fd, $data) {
        $this->sendMessage($fd, [
            'type' => 'echo',
            'original' => $data,
            'timestamp' => microtime(true)
        ]);
    }
    
    protected function handleBroadcast($fd, $data) {
        $message = [
            'type' => 'broadcast',
            'from' => $fd,
            'message' => $data['message'] ?? '',
            'timestamp' => microtime(true)
        ];
        
        $this->broadcast($message, $fd);
    }
    
    protected function handlePrivateMessage($fd, $data) {
        $targetFd = $data['target'] ?? null;
        
        if (!$targetFd || !$this->server->isEstablished($targetFd)) {
            $this->sendError($fd, 'Target connection not found');
            return;
        }
        
        $message = [
            'type' => 'private',
            'from' => $fd,
            'message' => $data['message'] ?? '',
            'timestamp' => microtime(true)
        ];
        
        $this->sendMessage($targetFd, $message);
    }
    
    protected function handleClose($server, $fd) {
        // 子类可以重写此方法处理连接关闭
    }
    
    protected function handleTask($data) {
        // 子类可以重写此方法处理异步任务
        return ['status' => 'completed', 'data' => $data];
    }
    
    protected function sendMessage($fd, $data) {
        if ($this->server->isEstablished($fd)) {
            $json = json_encode($data);
            $result = $this->server->push($fd, $json);
            
            if ($result) {
                $this->stats['messages_sent']++;
            }
            
            return $result;
        }
        
        return false;
    }
    
    protected function sendError($fd, $message) {
        $this->sendMessage($fd, [
            'type' => 'error',
            'message' => $message,
            'timestamp' => microtime(true)
        ]);
    }
    
    protected function broadcast($data, $excludeFd = null) {
        $count = 0;
        
        foreach ($this->server->connections as $fd) {
            if ($fd !== $excludeFd && $this->server->isEstablished($fd)) {
                if ($this->sendMessage($fd, $data)) {
                    $count++;
                }
            }
        }
        
        return $count;
    }
    
    protected function logStats() {
        $uptime = time() - $this->stats['start_time'];
        $memoryUsage = memory_get_usage(true);
        
        echo "=== 服务器统计 ===\n";
        echo "运行时间: {$uptime} 秒\n";
        echo "当前连接数: {$this->stats['current_connections']}\n";
        echo "总连接数: {$this->stats['total_connections']}\n";
        echo "已发送消息: {$this->stats['messages_sent']}\n";
        echo "已接收消息: {$this->stats['messages_received']}\n";
        echo "内存使用: " . round($memoryUsage / 1024 / 1024, 2) . " MB\n";
        echo "==================\n";
    }
    
    protected function cleanupConnections() {
        $now = time();
        $timeout = 300; // 5分钟无消息则认为连接异常
        
        foreach ($this->connections as $fd => $connection) {
            if (($now - $connection['last_message_time']) > $timeout) {
                if ($this->server->isEstablished($fd)) {
                    echo "清理超时连接: FD={$fd}\n";
                    $this->server->close($fd);
                }
            }
        }
    }
    
    protected function serveHomePage($response) {
        $html = $this->getTestPageHtml();
        $response->header('Content-Type', 'text/html; charset=utf-8');
        $response->end($html);
    }
    
    protected function serveStats($response) {
        $stats = array_merge($this->stats, [
            'uptime' => time() - $this->stats['start_time'],
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'connection_details' => count($this->connections)
        ]);
        
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($stats, JSON_PRETTY_PRINT));
    }
    
    protected function serveConnections($response) {
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($this->connections, JSON_PRETTY_PRINT));
    }
    
    protected function getTestPageHtml() {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket 测试页面</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .messages { height: 400px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; margin: 10px 0; }
        .controls { margin: 10px 0; }
        input, button, select { padding: 8px; margin: 2px; }
        input[type="text"] { width: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 服务器测试</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="controls">
            <button id="connect">连接</button>
            <button id="disconnect" disabled>断开</button>
            <button id="stats">获取统计</button>
            <button id="clear">清空消息</button>
        </div>
        
        <div id="messages" class="messages"></div>
        
        <div class="controls">
            <select id="msgType">
                <option value="ping">Ping</option>
                <option value="echo">Echo</option>
                <option value="broadcast">广播</option>
            </select>
            <input type="text" id="msgInput" placeholder="输入消息" disabled>
            <button id="send" disabled>发送</button>
        </div>
    </div>
    
    <script>
        let ws = null;
        const status = document.getElementById("status");
        const messages = document.getElementById("messages");
        
        function log(msg, type = "info") {
            const div = document.createElement("div");
            div.style.color = type === "error" ? "red" : type === "sent" ? "blue" : "green";
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function updateStatus(connected) {
            status.textContent = connected ? "已连接" : "未连接";
            status.className = `status ${connected ? "connected" : "disconnected"}`;
            document.getElementById("connect").disabled = connected;
            document.getElementById("disconnect").disabled = !connected;
            document.getElementById("msgInput").disabled = !connected;
            document.getElementById("send").disabled = !connected;
        }
        
        document.getElementById("connect").onclick = () => {
            ws = new WebSocket(`ws://${location.host}`);
            
            ws.onopen = () => {
                log("WebSocket 连接已建立");
                updateStatus(true);
            };
            
            ws.onmessage = (event) => {
                log(`收到: ${event.data}`);
            };
            
            ws.onclose = () => {
                log("WebSocket 连接已关闭");
                updateStatus(false);
            };
            
            ws.onerror = (error) => {
                log("WebSocket 错误", "error");
            };
        };
        
        document.getElementById("disconnect").onclick = () => {
            if (ws) ws.close();
        };
        
        document.getElementById("send").onclick = () => {
            const type = document.getElementById("msgType").value;
            const message = document.getElementById("msgInput").value;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                const data = { type, message };
                ws.send(JSON.stringify(data));
                log(`发送: ${JSON.stringify(data)}`, "sent");
                document.getElementById("msgInput").value = "";
            }
        };
        
        document.getElementById("stats").onclick = () => {
            fetch("/stats")
                .then(r => r.json())
                .then(data => log(`统计: ${JSON.stringify(data, null, 2)}`));
        };
        
        document.getElementById("clear").onclick = () => {
            messages.innerHTML = "";
        };
        
        document.getElementById("msgInput").onkeypress = (e) => {
            if (e.key === "Enter") document.getElementById("send").click();
        };
    </script>
</body>
</html>';
    }
    
    public function start() {
        $this->server->start();
    }
}

// 使用示例
$server = new WebSocketServerBase('0.0.0.0', 9501, [
    'worker_num' => 2,
    'max_conn' => 1000,
    'heartbeat_check_interval' => 30,
    'heartbeat_idle_time' => 300,
]);

$server->start();
?>
```

## 11.2 连接管理系统

### 11.2.1 连接池管理

```php
<?php
// connection_manager.php

use Swoole\WebSocket\Server;
use Swoole\Table;

class ConnectionManager {
    private $connections;
    private $userConnections;
    private $roomConnections;
    private $server;

    public function __construct(Server $server) {
        $this->server = $server;
        $this->initializeTables();
    }

    private function initializeTables() {
        // 连接信息表
        $this->connections = new Table(10000);
        $this->connections->column('fd', Table::TYPE_INT);
        $this->connections->column('user_id', Table::TYPE_STRING, 64);
        $this->connections->column('ip', Table::TYPE_STRING, 16);
        $this->connections->column('connect_time', Table::TYPE_INT);
        $this->connections->column('last_active', Table::TYPE_INT);
        $this->connections->column('room_id', Table::TYPE_STRING, 64);
        $this->connections->column('status', Table::TYPE_STRING, 16);
        $this->connections->create();

        // 用户连接映射表
        $this->userConnections = new Table(10000);
        $this->userConnections->column('fd', Table::TYPE_INT);
        $this->userConnections->column('connect_time', Table::TYPE_INT);
        $this->userConnections->create();

        // 房间连接映射表
        $this->roomConnections = new Table(1000);
        $this->roomConnections->column('connection_count', Table::TYPE_INT);
        $this->roomConnections->column('created_time', Table::TYPE_INT);
        $this->roomConnections->create();
    }

    public function addConnection($fd, $data = []) {
        $now = time();

        $connectionData = [
            'fd' => $fd,
            'user_id' => $data['user_id'] ?? '',
            'ip' => $data['ip'] ?? '',
            'connect_time' => $now,
            'last_active' => $now,
            'room_id' => $data['room_id'] ?? '',
            'status' => 'active'
        ];

        $this->connections->set($fd, $connectionData);

        // 更新用户连接映射
        if (!empty($data['user_id'])) {
            $this->userConnections->set($data['user_id'], [
                'fd' => $fd,
                'connect_time' => $now
            ]);
        }

        // 更新房间连接计数
        if (!empty($data['room_id'])) {
            $this->joinRoom($fd, $data['room_id']);
        }

        echo "连接已添加: FD={$fd}, User={$data['user_id']}, Room={$data['room_id']}\n";
    }

    public function removeConnection($fd) {
        $connection = $this->connections->get($fd);

        if ($connection) {
            // 从用户映射中移除
            if (!empty($connection['user_id'])) {
                $this->userConnections->del($connection['user_id']);
            }

            // 从房间中移除
            if (!empty($connection['room_id'])) {
                $this->leaveRoom($fd, $connection['room_id']);
            }

            $this->connections->del($fd);

            echo "连接已移除: FD={$fd}, User={$connection['user_id']}\n";
        }
    }

    public function updateLastActive($fd) {
        $connection = $this->connections->get($fd);
        if ($connection) {
            $connection['last_active'] = time();
            $this->connections->set($fd, $connection);
        }
    }

    public function joinRoom($fd, $roomId) {
        $connection = $this->connections->get($fd);
        if (!$connection) {
            return false;
        }

        // 如果已在其他房间，先离开
        if (!empty($connection['room_id']) && $connection['room_id'] !== $roomId) {
            $this->leaveRoom($fd, $connection['room_id']);
        }

        // 加入新房间
        $connection['room_id'] = $roomId;
        $this->connections->set($fd, $connection);

        // 更新房间连接计数
        $roomData = $this->roomConnections->get($roomId);
        if ($roomData) {
            $roomData['connection_count']++;
        } else {
            $roomData = [
                'connection_count' => 1,
                'created_time' => time()
            ];
        }
        $this->roomConnections->set($roomId, $roomData);

        echo "用户加入房间: FD={$fd}, Room={$roomId}\n";
        return true;
    }

    public function leaveRoom($fd, $roomId) {
        $connection = $this->connections->get($fd);
        if (!$connection || $connection['room_id'] !== $roomId) {
            return false;
        }

        // 从连接中移除房间信息
        $connection['room_id'] = '';
        $this->connections->set($fd, $connection);

        // 更新房间连接计数
        $roomData = $this->roomConnections->get($roomId);
        if ($roomData) {
            $roomData['connection_count']--;
            if ($roomData['connection_count'] <= 0) {
                $this->roomConnections->del($roomId);
            } else {
                $this->roomConnections->set($roomId, $roomData);
            }
        }

        echo "用户离开房间: FD={$fd}, Room={$roomId}\n";
        return true;
    }

    public function getConnection($fd) {
        return $this->connections->get($fd);
    }

    public function getUserConnection($userId) {
        $userConn = $this->userConnections->get($userId);
        if ($userConn) {
            return $this->connections->get($userConn['fd']);
        }
        return null;
    }

    public function getRoomConnections($roomId) {
        $connections = [];

        foreach ($this->connections as $fd => $connection) {
            if ($connection['room_id'] === $roomId) {
                $connections[] = $connection;
            }
        }

        return $connections;
    }

    public function getRoomConnectionCount($roomId) {
        $roomData = $this->roomConnections->get($roomId);
        return $roomData ? $roomData['connection_count'] : 0;
    }

    public function getAllRooms() {
        $rooms = [];

        foreach ($this->roomConnections as $roomId => $roomData) {
            $rooms[$roomId] = $roomData;
        }

        return $rooms;
    }

    public function broadcastToRoom($roomId, $message, $excludeFd = null) {
        $connections = $this->getRoomConnections($roomId);
        $count = 0;

        foreach ($connections as $connection) {
            $fd = $connection['fd'];
            if ($fd !== $excludeFd && $this->server->isEstablished($fd)) {
                if ($this->server->push($fd, json_encode($message))) {
                    $count++;
                }
            }
        }

        return $count;
    }

    public function broadcastToUser($userId, $message) {
        $connection = $this->getUserConnection($userId);
        if ($connection && $this->server->isEstablished($connection['fd'])) {
            return $this->server->push($connection['fd'], json_encode($message));
        }
        return false;
    }

    public function getStats() {
        $totalConnections = 0;
        $activeConnections = 0;
        $roomCount = 0;
        $userCount = 0;

        foreach ($this->connections as $fd => $connection) {
            $totalConnections++;
            if ($connection['status'] === 'active') {
                $activeConnections++;
            }
        }

        foreach ($this->roomConnections as $roomId => $roomData) {
            $roomCount++;
        }

        foreach ($this->userConnections as $userId => $userData) {
            $userCount++;
        }

        return [
            'total_connections' => $totalConnections,
            'active_connections' => $activeConnections,
            'room_count' => $roomCount,
            'user_count' => $userCount,
            'memory_usage' => [
                'connections' => $this->connections->memorySize,
                'users' => $this->userConnections->memorySize,
                'rooms' => $this->roomConnections->memorySize,
            ]
        ];
    }

    public function cleanupInactiveConnections($timeout = 300) {
        $now = time();
        $cleaned = 0;

        foreach ($this->connections as $fd => $connection) {
            if (($now - $connection['last_active']) > $timeout) {
                if (!$this->server->isEstablished($fd)) {
                    $this->removeConnection($fd);
                    $cleaned++;
                }
            }
        }

        return $cleaned;
    }
}

// 使用连接管理器的 WebSocket 服务器
class ManagedWebSocketServer {
    private $server;
    private $connectionManager;

    public function __construct($host = '0.0.0.0', $port = 9501) {
        $this->server = new Server($host, $port);
        $this->connectionManager = new ConnectionManager($this->server);
        $this->setupEvents();
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('request', [$this, 'onRequest']);
    }

    public function onOpen($server, $request) {
        $this->connectionManager->addConnection($request->fd, [
            'ip' => $request->server['remote_addr'],
        ]);

        // 发送欢迎消息
        $server->push($request->fd, json_encode([
            'type' => 'welcome',
            'message' => '欢迎连接到管理型 WebSocket 服务器',
            'fd' => $request->fd
        ]));
    }

    public function onMessage($server, $frame) {
        $this->connectionManager->updateLastActive($frame->fd);

        $data = json_decode($frame->data, true);
        if (!$data) {
            return;
        }

        switch ($data['type']) {
            case 'join_room':
                $this->handleJoinRoom($frame->fd, $data);
                break;
            case 'leave_room':
                $this->handleLeaveRoom($frame->fd, $data);
                break;
            case 'room_message':
                $this->handleRoomMessage($frame->fd, $data);
                break;
            case 'private_message':
                $this->handlePrivateMessage($frame->fd, $data);
                break;
            case 'get_stats':
                $this->handleGetStats($frame->fd);
                break;
        }
    }

    public function onClose($server, $fd) {
        $this->connectionManager->removeConnection($fd);
    }

    public function onRequest($request, $response) {
        if ($request->server['request_uri'] === '/stats') {
            $stats = $this->connectionManager->getStats();
            $response->header('Content-Type', 'application/json');
            $response->end(json_encode($stats, JSON_PRETTY_PRINT));
        } else {
            $response->status(404);
            $response->end('Not Found');
        }
    }

    private function handleJoinRoom($fd, $data) {
        $roomId = $data['room_id'] ?? '';
        if (empty($roomId)) {
            return;
        }

        $success = $this->connectionManager->joinRoom($fd, $roomId);

        $this->server->push($fd, json_encode([
            'type' => 'join_room_result',
            'success' => $success,
            'room_id' => $roomId,
            'room_count' => $this->connectionManager->getRoomConnectionCount($roomId)
        ]));

        if ($success) {
            // 通知房间其他成员
            $this->connectionManager->broadcastToRoom($roomId, [
                'type' => 'user_joined',
                'fd' => $fd,
                'room_id' => $roomId
            ], $fd);
        }
    }

    private function handleLeaveRoom($fd, $data) {
        $connection = $this->connectionManager->getConnection($fd);
        if (!$connection || empty($connection['room_id'])) {
            return;
        }

        $roomId = $connection['room_id'];
        $success = $this->connectionManager->leaveRoom($fd, $roomId);

        $this->server->push($fd, json_encode([
            'type' => 'leave_room_result',
            'success' => $success,
            'room_id' => $roomId
        ]));

        if ($success) {
            // 通知房间其他成员
            $this->connectionManager->broadcastToRoom($roomId, [
                'type' => 'user_left',
                'fd' => $fd,
                'room_id' => $roomId
            ], $fd);
        }
    }

    private function handleRoomMessage($fd, $data) {
        $connection = $this->connectionManager->getConnection($fd);
        if (!$connection || empty($connection['room_id'])) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => 'You are not in any room'
            ]));
            return;
        }

        $message = [
            'type' => 'room_message',
            'from' => $fd,
            'room_id' => $connection['room_id'],
            'message' => $data['message'] ?? '',
            'timestamp' => time()
        ];

        $count = $this->connectionManager->broadcastToRoom($connection['room_id'], $message);

        echo "房间消息广播: Room={$connection['room_id']}, 接收者={$count}\n";
    }

    private function handlePrivateMessage($fd, $data) {
        $targetUserId = $data['target_user'] ?? '';
        if (empty($targetUserId)) {
            return;
        }

        $message = [
            'type' => 'private_message',
            'from' => $fd,
            'message' => $data['message'] ?? '',
            'timestamp' => time()
        ];

        $success = $this->connectionManager->broadcastToUser($targetUserId, $message);

        $this->server->push($fd, json_encode([
            'type' => 'private_message_result',
            'success' => $success,
            'target_user' => $targetUserId
        ]));
    }

    private function handleGetStats($fd) {
        $stats = $this->connectionManager->getStats();

        $this->server->push($fd, json_encode([
            'type' => 'stats',
            'data' => $stats
        ]));
    }

    public function start() {
        echo "管理型 WebSocket 服务器启动成功\n";
        $this->server->start();
    }
}

$server = new ManagedWebSocketServer('0.0.0.0', 9501);
$server->start();
?>
```

## 11.3 消息路由系统

### 11.3.1 消息路由器

```php
<?php
// message_router.php

use Swoole\WebSocket\Server;

class MessageRouter {
    private $routes = [];
    private $middleware = [];
    private $server;

    public function __construct(Server $server) {
        $this->server = $server;
    }

    public function addRoute($type, $handler, $middleware = []) {
        $this->routes[$type] = [
            'handler' => $handler,
            'middleware' => $middleware
        ];
    }

    public function addMiddleware($middleware) {
        $this->middleware[] = $middleware;
    }

    public function route($fd, $message) {
        $data = json_decode($message, true);

        if (!$data || !isset($data['type'])) {
            $this->sendError($fd, 'Invalid message format');
            return;
        }

        $type = $data['type'];

        if (!isset($this->routes[$type])) {
            $this->sendError($fd, "Unknown message type: {$type}");
            return;
        }

        $route = $this->routes[$type];

        // 执行中间件链
        $this->executeMiddleware($fd, $data, $route);
    }

    private function executeMiddleware($fd, $data, $route) {
        $allMiddleware = array_merge($this->middleware, $route['middleware']);
        $index = 0;

        $next = function($fd, $data) use (&$next, &$index, $allMiddleware, $route) {
            if ($index < count($allMiddleware)) {
                $middleware = $allMiddleware[$index++];
                return $middleware($fd, $data, $next);
            } else {
                return $route['handler']($fd, $data);
            }
        };

        $next($fd, $data);
    }

    private function sendError($fd, $message) {
        if ($this->server->isEstablished($fd)) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => $message,
                'timestamp' => microtime(true)
            ]));
        }
    }
}

// 中间件示例
class AuthMiddleware {
    private $authenticatedUsers = [];

    public function __invoke($fd, $data, $next) {
        // 检查是否需要认证
        $protectedTypes = ['send_message', 'join_room', 'private_message'];

        if (in_array($data['type'], $protectedTypes)) {
            if (!isset($this->authenticatedUsers[$fd])) {
                $this->sendAuthError($fd);
                return;
            }
        }

        return $next($fd, $data);
    }

    public function authenticate($fd, $token) {
        // 简单的认证逻辑
        if ($this->validateToken($token)) {
            $this->authenticatedUsers[$fd] = [
                'token' => $token,
                'auth_time' => time()
            ];
            return true;
        }
        return false;
    }

    public function logout($fd) {
        unset($this->authenticatedUsers[$fd]);
    }

    private function validateToken($token) {
        // 实际应用中应该验证 JWT 或查询数据库
        return $token === 'valid-token-123';
    }

    private function sendAuthError($fd) {
        global $server;
        if ($server->isEstablished($fd)) {
            $server->push($fd, json_encode([
                'type' => 'auth_error',
                'message' => 'Authentication required'
            ]));
        }
    }
}

class RateLimitMiddleware {
    private $requests = [];
    private $maxRequests;
    private $timeWindow;

    public function __construct($maxRequests = 10, $timeWindow = 60) {
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow;
    }

    public function __invoke($fd, $data, $next) {
        $now = time();

        if (!isset($this->requests[$fd])) {
            $this->requests[$fd] = [];
        }

        // 清理过期请求
        $this->requests[$fd] = array_filter($this->requests[$fd], function($timestamp) use ($now) {
            return ($now - $timestamp) < $this->timeWindow;
        });

        if (count($this->requests[$fd]) >= $this->maxRequests) {
            $this->sendRateLimitError($fd);
            return;
        }

        $this->requests[$fd][] = $now;
        return $next($fd, $data);
    }

    private function sendRateLimitError($fd) {
        global $server;
        if ($server->isEstablished($fd)) {
            $server->push($fd, json_encode([
                'type' => 'rate_limit_error',
                'message' => 'Too many requests'
            ]));
        }
    }
}

class LoggingMiddleware {
    public function __invoke($fd, $data, $next) {
        $start = microtime(true);

        echo "[" . date('Y-m-d H:i:s') . "] FD={$fd}, Type={$data['type']}\n";

        $result = $next($fd, $data);

        $duration = round((microtime(true) - $start) * 1000, 2);
        echo "  -> 处理耗时: {$duration}ms\n";

        return $result;
    }
}

// 路由处理器
class MessageHandlers {
    private $server;
    private $connectionManager;
    private $authMiddleware;

    public function __construct(Server $server, $connectionManager, $authMiddleware) {
        $this->server = $server;
        $this->connectionManager = $connectionManager;
        $this->authMiddleware = $authMiddleware;
    }

    public function handleAuth($fd, $data) {
        $token = $data['token'] ?? '';

        if ($this->authMiddleware->authenticate($fd, $token)) {
            $this->server->push($fd, json_encode([
                'type' => 'auth_success',
                'message' => 'Authentication successful'
            ]));
        } else {
            $this->server->push($fd, json_encode([
                'type' => 'auth_failed',
                'message' => 'Invalid token'
            ]));
        }
    }

    public function handlePing($fd, $data) {
        $this->server->push($fd, json_encode([
            'type' => 'pong',
            'timestamp' => microtime(true)
        ]));
    }

    public function handleJoinRoom($fd, $data) {
        $roomId = $data['room_id'] ?? '';
        if (empty($roomId)) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => 'Room ID is required'
            ]));
            return;
        }

        $success = $this->connectionManager->joinRoom($fd, $roomId);

        $this->server->push($fd, json_encode([
            'type' => 'join_room_result',
            'success' => $success,
            'room_id' => $roomId
        ]));

        if ($success) {
            $this->connectionManager->broadcastToRoom($roomId, [
                'type' => 'user_joined_room',
                'user_fd' => $fd,
                'room_id' => $roomId
            ], $fd);
        }
    }

    public function handleSendMessage($fd, $data) {
        $connection = $this->connectionManager->getConnection($fd);
        if (!$connection || empty($connection['room_id'])) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => 'You must join a room first'
            ]));
            return;
        }

        $message = [
            'type' => 'room_message',
            'from' => $fd,
            'room_id' => $connection['room_id'],
            'message' => $data['message'] ?? '',
            'timestamp' => time()
        ];

        $this->connectionManager->broadcastToRoom($connection['room_id'], $message);
    }

    public function handlePrivateMessage($fd, $data) {
        $targetFd = $data['target_fd'] ?? null;

        if (!$targetFd || !$this->server->isEstablished($targetFd)) {
            $this->server->push($fd, json_encode([
                'type' => 'error',
                'message' => 'Target user not found or offline'
            ]));
            return;
        }

        $message = [
            'type' => 'private_message',
            'from' => $fd,
            'message' => $data['message'] ?? '',
            'timestamp' => time()
        ];

        $this->server->push($targetFd, json_encode($message));

        // 发送确认给发送者
        $this->server->push($fd, json_encode([
            'type' => 'private_message_sent',
            'target_fd' => $targetFd
        ]));
    }
}

// 使用消息路由的 WebSocket 服务器
class RoutedWebSocketServer {
    private $server;
    private $router;
    private $connectionManager;
    private $authMiddleware;
    private $handlers;

    public function __construct($host = '0.0.0.0', $port = 9501) {
        $this->server = new Server($host, $port);
        $this->connectionManager = new ConnectionManager($this->server);
        $this->router = new MessageRouter($this->server);
        $this->authMiddleware = new AuthMiddleware();
        $this->handlers = new MessageHandlers($this->server, $this->connectionManager, $this->authMiddleware);

        $this->setupRouter();
        $this->setupEvents();
    }

    private function setupRouter() {
        // 添加全局中间件
        $this->router->addMiddleware(new LoggingMiddleware());
        $this->router->addMiddleware(new RateLimitMiddleware(20, 60));

        // 添加路由
        $this->router->addRoute('auth', [$this->handlers, 'handleAuth']);
        $this->router->addRoute('ping', [$this->handlers, 'handlePing']);
        $this->router->addRoute('join_room', [$this->handlers, 'handleJoinRoom'], [$this->authMiddleware]);
        $this->router->addRoute('send_message', [$this->handlers, 'handleSendMessage'], [$this->authMiddleware]);
        $this->router->addRoute('private_message', [$this->handlers, 'handlePrivateMessage'], [$this->authMiddleware]);
    }

    private function setupEvents() {
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);
        $this->server->on('request', [$this, 'onRequest']);
    }

    public function onOpen($server, $request) {
        $this->connectionManager->addConnection($request->fd, [
            'ip' => $request->server['remote_addr']
        ]);

        $server->push($request->fd, json_encode([
            'type' => 'welcome',
            'message' => '欢迎连接到路由型 WebSocket 服务器',
            'fd' => $request->fd,
            'auth_required' => true
        ]));
    }

    public function onMessage($server, $frame) {
        $this->connectionManager->updateLastActive($frame->fd);
        $this->router->route($frame->fd, $frame->data);
    }

    public function onClose($server, $fd) {
        $this->authMiddleware->logout($fd);
        $this->connectionManager->removeConnection($fd);
    }

    public function onRequest($request, $response) {
        if ($request->server['request_uri'] === '/') {
            $this->serveTestPage($response);
        } elseif ($request->server['request_uri'] === '/stats') {
            $stats = $this->connectionManager->getStats();
            $response->header('Content-Type', 'application/json');
            $response->end(json_encode($stats, JSON_PRETTY_PRINT));
        } else {
            $response->status(404);
            $response->end('Not Found');
        }
    }

    private function serveTestPage($response) {
        $html = '
<!DOCTYPE html>
<html>
<head>
    <title>路由型 WebSocket 测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .messages { height: 400px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; margin: 10px 0; }
        .controls { margin: 10px 0; }
        input, button, select { padding: 8px; margin: 2px; }
        input[type="text"] { width: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>路由型 WebSocket 服务器测试</h1>

        <div id="status" class="status disconnected">未连接</div>

        <div class="controls">
            <button id="connect">连接</button>
            <button id="disconnect" disabled>断开</button>
            <input type="text" id="token" placeholder="认证令牌" value="valid-token-123">
            <button id="auth" disabled>认证</button>
        </div>

        <div id="messages" class="messages"></div>

        <div class="controls">
            <input type="text" id="roomId" placeholder="房间ID" disabled>
            <button id="joinRoom" disabled>加入房间</button>
        </div>

        <div class="controls">
            <input type="text" id="messageText" placeholder="消息内容" disabled>
            <button id="sendMessage" disabled>发送消息</button>
            <button id="ping" disabled>Ping</button>
        </div>
    </div>

    <script>
        let ws = null;
        let authenticated = false;

        function log(msg, type = "info") {
            const div = document.createElement("div");
            div.style.color = type === "error" ? "red" : type === "sent" ? "blue" : "green";
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById("messages").appendChild(div);
            div.scrollIntoView();
        }

        function updateStatus(connected, auth = false) {
            const status = document.getElementById("status");
            status.textContent = connected ? (auth ? "已连接并认证" : "已连接未认证") : "未连接";
            status.className = `status ${connected ? "connected" : "disconnected"}`;

            document.getElementById("connect").disabled = connected;
            document.getElementById("disconnect").disabled = !connected;
            document.getElementById("auth").disabled = !connected || auth;
            document.getElementById("joinRoom").disabled = !auth;
            document.getElementById("sendMessage").disabled = !auth;
            document.getElementById("ping").disabled = !connected;
            document.getElementById("roomId").disabled = !auth;
            document.getElementById("messageText").disabled = !auth;

            authenticated = auth;
        }

        document.getElementById("connect").onclick = () => {
            ws = new WebSocket(`ws://${location.host}`);

            ws.onopen = () => {
                log("WebSocket 连接已建立");
                updateStatus(true, false);
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                log(`收到: ${JSON.stringify(data)}`);

                if (data.type === "auth_success") {
                    updateStatus(true, true);
                }
            };

            ws.onclose = () => {
                log("WebSocket 连接已关闭");
                updateStatus(false, false);
            };
        };

        document.getElementById("disconnect").onclick = () => {
            if (ws) ws.close();
        };

        document.getElementById("auth").onclick = () => {
            const token = document.getElementById("token").value;
            if (ws && token) {
                ws.send(JSON.stringify({ type: "auth", token }));
                log(`发送认证: ${token}`, "sent");
            }
        };

        document.getElementById("joinRoom").onclick = () => {
            const roomId = document.getElementById("roomId").value;
            if (ws && roomId && authenticated) {
                ws.send(JSON.stringify({ type: "join_room", room_id: roomId }));
                log(`加入房间: ${roomId}`, "sent");
            }
        };

        document.getElementById("sendMessage").onclick = () => {
            const message = document.getElementById("messageText").value;
            if (ws && message && authenticated) {
                ws.send(JSON.stringify({ type: "send_message", message }));
                log(`发送消息: ${message}`, "sent");
                document.getElementById("messageText").value = "";
            }
        };

        document.getElementById("ping").onclick = () => {
            if (ws) {
                ws.send(JSON.stringify({ type: "ping" }));
                log("发送 Ping", "sent");
            }
        };
    </script>
</body>
</html>';

        $response->header('Content-Type', 'text/html; charset=utf-8');
        $response->end($html);
    }

    public function start() {
        echo "路由型 WebSocket 服务器启动成功\n";
        $this->server->start();
    }
}

$server = new RoutedWebSocketServer('0.0.0.0', 9501);
$server->start();
?>
```

## 本章练习

### 练习 1：WebSocket 服务器监控系统
创建一个完整的 WebSocket 服务器监控系统：

```php
<?php
// websocket_monitor.php

class WebSocketMonitor {
    // 实现功能：
    // 1. 实时连接数监控
    // 2. 消息吞吐量统计
    // 3. 错误率监控
    // 4. 性能指标收集
    // 5. 告警系统
    // 6. 监控数据可视化
}

class MetricsCollector {
    // 实现功能：
    // 1. 连接生命周期跟踪
    // 2. 消息处理时间统计
    // 3. 内存使用监控
    // 4. CPU 使用率监控
    // 5. 网络 I/O 统计
}
?>
```

### 练习 2：WebSocket 负载均衡器
实现 WebSocket 服务器的负载均衡：

```php
<?php
// websocket_load_balancer.php

class WebSocketLoadBalancer {
    // 实现功能：
    // 1. 多服务器实例管理
    // 2. 连接分发策略
    // 3. 健康检查
    // 4. 故障转移
    // 5. 会话粘性
    // 6. 跨服务器消息路由
}

class ServerPool {
    // 实现功能：
    // 1. 服务器注册和发现
    // 2. 负载均衡算法
    // 3. 服务器状态监控
    // 4. 动态扩缩容
}
?>
```

### 练习 3：WebSocket 消息持久化
实现消息的持久化存储和恢复：

```php
<?php
// websocket_persistence.php

class MessagePersistence {
    // 实现功能：
    // 1. 消息存储到数据库
    // 2. 离线消息队列
    // 3. 消息历史查询
    // 4. 消息确认机制
    // 5. 消息重发机制
    // 6. 消息过期清理
}

class OfflineMessageQueue {
    // 实现功能：
    // 1. 用户离线消息存储
    // 2. 用户上线时消息推送
    // 3. 消息优先级处理
    // 4. 消息去重
}
?>
```

### 练习 4：WebSocket 安全增强
实现高级安全功能：

```php
<?php
// websocket_security.php

class WebSocketSecurity {
    // 实现功能：
    // 1. JWT 认证集成
    // 2. 权限控制系统
    // 3. 消息加密传输
    // 4. 防重放攻击
    // 5. IP 白名单/黑名单
    // 6. 异常行为检测
}

class SecurityAudit {
    // 实现功能：
    // 1. 安全事件日志
    // 2. 攻击检测和防护
    // 3. 安全报告生成
    // 4. 合规性检查
}
?>
```

## 本章小结

本章深入介绍了 WebSocket 服务器的完整开发流程，从基础架构到高级特性，涵盖了现代 WebSocket 应用开发的各个方面。

**关键要点：**

- **服务器架构**：模块化设计、事件驱动、可扩展性
- **连接管理**：生命周期管理、状态跟踪、资源清理
- **消息路由**：类型路由、中间件链、处理器模式
- **性能优化**：连接池、消息缓存、异步处理
- **监控运维**：统计收集、性能监控、故障诊断

**设计模式：**

1. **观察者模式**：事件驱动的消息处理
2. **中间件模式**：可插拔的消息处理链
3. **工厂模式**：连接和消息对象创建
4. **策略模式**：不同的路由和认证策略
5. **单例模式**：全局状态管理

**最佳实践：**

1. **分层架构**：清晰的职责分离
2. **异常处理**：完善的错误处理机制
3. **资源管理**：及时清理无效连接
4. **安全考虑**：认证、授权、限流
5. **性能监控**：实时监控和告警

**性能优化技巧：**

1. 使用连接池管理连接状态
2. 实现消息路由减少处理开销
3. 采用中间件模式提高代码复用
4. 合理设置心跳检测间隔
5. 使用异步任务处理耗时操作

**下一章预告：**
下一章我们将学习实时通信应用的开发，包括聊天室、协作工具、实时游戏等具体应用场景的实现。
